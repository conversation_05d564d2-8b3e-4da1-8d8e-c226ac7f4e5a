"use client";

import { useState } from "react";

import { CreateReportData, ReportConfig } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReportCreation() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createReport = async (
    reportConfig: ReportConfig,
    selectedFields: string[],
    fieldFilters: Record<string, any>,
  ): Promise<{ success: boolean; reportId?: number; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const reportData: CreateReportData = {
        reportConfig,
        selectedFields,
        fieldFilters,
      };

      const newReport = await ReportsService.createReport(reportData);

      return { success: true, reportId: newReport.id };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to create report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createReport,
  };
}
