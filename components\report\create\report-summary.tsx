"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAllFieldsQuery } from "@/graphql/schemas/generated";

interface ReportConfig {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}

interface FieldTypeFilter {
  statuses: ("completed" | "not_completed" | "in_progress")[];
  includeObservations: boolean;
}

interface ReportSummaryProps {
  reportConfig: ReportConfig;
  selectedFields: string[];
  fieldFilters: Record<string, FieldTypeFilter>;
  onCreateReport: () => void;
}

const getFieldTypeDisplayName = (fieldType: string) => {
  switch (fieldType.toLowerCase()) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "<PERSON><PERSON><PERSON>";
    default:
      return fieldType;
  }
};

const getFilterDisplayName = (filterOption: string): string => {
  switch (filterOption) {
    case "completed":
      return "Completado";
    case "not_completed":
      return "No completado";
    case "in_progress":
      return "En progreso";
    default:
      return filterOption;
  }
};

const getFilterColor = (filterOption: string) => {
  switch (filterOption) {
    case "completed":
      return "success";
    case "not_completed":
      return "danger";
    case "in_progress":
      return "warning";
    default:
      return "default";
  }
};

const getFilterDotColor = (filterOption: string) => {
  switch (filterOption) {
    case "completed":
      return "success";
    case "not_completed":
      return "danger";
    case "in_progress":
      return "warning";
    default:
      return "default";
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

export default function ReportSummary({
  reportConfig,
  selectedFields,
  fieldFilters,
  onCreateReport,
}: ReportSummaryProps) {
  const { data: fieldsData } = useAllFieldsQuery();

  const fields = fieldsData?.allFields || [];

  // Get selected field details
  const selectedFieldsData = fields.filter((field) =>
    selectedFields.includes(field?.id || ""),
  );

  // Group fields by type for summary
  const fieldsByType = selectedFieldsData.reduce(
    (acc, field) => {
      const type = field?.type || "unknown";

      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(field);

      return acc;
    },
    {} as Record<string, any[]>,
  );

  // Get filter count
  const filterCount = Object.keys(fieldFilters).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Resumen del reporte</h3>
        <p className="text-default-500">
          Revisa la configuración antes de crear el reporte
        </p>
      </div>

      {/* Report Configuration */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:document-text"
              width={20}
            />
            <h4 className="text-lg font-semibold">Configuración del reporte</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-default-600">Nombre:</span>
              <span className="font-medium">
                {reportConfig.name || "Sin nombre"}
              </span>
            </div>
            <div className="flex justify-between items-start">
              <span className="text-sm text-default-600">Descripción:</span>
              <span className="font-medium text-right max-w-xs">
                {reportConfig.description || "Sin descripción"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-default-600">Documento:</span>
              <span className="font-medium">
                {reportConfig.documentName || "Sin nombre"}
              </span>
            </div>
            <Divider />
            <div className="flex justify-between items-center">
              <span className="text-sm text-default-600">
                Idioma de campos:
              </span>
              <Chip
                color={reportConfig.useEnglishFields ? "secondary" : "primary"}
                size="sm"
                variant="flat"
              >
                {reportConfig.useEnglishFields ? "Inglés" : "Español"}
              </Chip>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-default-600">Observaciones:</span>
              <Chip
                color={reportConfig.includeObservations ? "success" : "default"}
                size="sm"
                variant="flat"
              >
                {reportConfig.includeObservations
                  ? "Incluidas"
                  : "No incluidas"}
              </Chip>
            </div>
          </div>
        </CardBody>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon className="text-primary" icon="heroicons:funnel" width={20} />
            <h4 className="text-lg font-semibold">Filtros configurados</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-default-600">
                Total de filtros:
              </span>
              <Chip color="secondary" size="sm" variant="flat">
                {filterCount}
              </Chip>
            </div>
            <Divider />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(fieldsByType).map(([type, fieldsOfType]) => {
                const filterConfig = fieldFilters[type] || {
                  statuses: ["completed"],
                  includeObservations: false,
                };

                return (
                  <Card key={type} className="shadow-sm">
                    <CardBody className="p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon
                          className="text-default-500"
                          icon={getFieldTypeIcon(type)}
                          width={16}
                        />
                        <span className="text-sm font-medium">
                          {getFieldTypeDisplayName(type)}
                        </span>
                        <Chip className="ml-auto" size="sm" variant="flat">
                          {fieldsOfType.length} campo(s)
                        </Chip>
                      </div>

                      <div className="space-y-2 text-xs text-default-600">
                        <div className="flex flex-wrap items-center gap-2">
                          <span className="font-medium">Estados:</span>
                          <div className="flex flex-wrap gap-1">
                            {filterConfig.statuses.map((status) => (
                              <Chip
                                key={status}
                                size="sm"
                                variant="dot"
                                color={getFilterDotColor(status)}
                              >
                                {getFilterDisplayName(status)}
                              </Chip>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="font-medium">Observaciones:</span>
                          <Chip
                            color={
                              filterConfig.includeObservations
                                ? "success"
                                : "default"
                            }
                            size="sm"
                            variant="flat"
                          >
                            {filterConfig.includeObservations
                              ? "Incluidas"
                              : "No incluidas"}
                          </Chip>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                );
              })}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Create Button */}
      <div className="flex justify-center pt-4">
        <Button
          color="primary"
          size="lg"
          startContent={<Icon icon="heroicons:document-plus" width={20} />}
          onPress={onCreateReport}
        >
          Crear Reporte
        </Button>
      </div>
    </div>
  );
}
