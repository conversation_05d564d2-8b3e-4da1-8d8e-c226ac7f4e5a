import { create } from "zustand";
import { persist } from "zustand/middleware";

import { User } from "@/types/user";

interface UserStore {
  userInfo: User | null;
  setUserInfo: (user: User | null) => void;
}

export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      userInfo: null,
      setUserInfo: (user) => set({ userInfo: user }),
    }),
    {
      name: "user-storage",
    },
  ),
);
