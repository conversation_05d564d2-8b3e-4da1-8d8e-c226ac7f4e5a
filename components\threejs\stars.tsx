"use client";

import { useState, useRef } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { Points, PointMaterial } from "@react-three/drei";
import * as random from "maath/random";

import { useStarsbgStore } from "@/store/use-starsbg-store";

export function Stars({ ...props }) {
  // @ts-ignore
  const ref = useRef<Points>(null);
  const [sphere] = useState(() =>
    random.inSphere(new Float32Array(1000), { radius: 1.5 }),
  );

  useFrame((state, delta) => {
    if (ref.current) {
      ref.current.rotation.x -= delta / 10;
      ref.current.rotation.y -= delta / 15;
    }
  });

  return (
    <group>
      <Points
        ref={ref}
        frustumCulled={false}
        positions={sphere as Float32Array}
        stride={3}
        {...props}
      >
        <PointMaterial
          transparent
          color="#E8E8E8"
          depthWrite={false}
          size={0.005}
          sizeAttenuation={true}
        />
      </Points>
    </group>
  );
}

export function OverlayStars() {
  const { starsEnabled } = useStarsbgStore();

  return (
    <div className="fixed inset-0">
      <div className="absolute inset-0" />
      {starsEnabled && (
        <Canvas camera={{ position: [0, 0, 1], fov: 75 }}>
          <Stars />
        </Canvas>
      )}
    </div>
  );
}
