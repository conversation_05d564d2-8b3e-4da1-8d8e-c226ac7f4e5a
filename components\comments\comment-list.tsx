import React, { useMemo } from "react";
import { User, Ava<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";

interface CommentListProps {
  // comments: {
  //   comments: Comment[];
  // };
  comments: any;
}

const avatarGradients = [
  "linear-gradient(300deg, rgb(250, 231, 191), rgb(255, 207, 191))",
  "linear-gradient(300deg, rgb(173, 216, 255), rgb(198, 189, 255))",
  "linear-gradient(300deg, rgb(191, 255, 215), rgb(196, 232, 250))",
  "linear-gradient(300deg, rgb(255, 215, 223), rgb(255, 225, 208))",
  "linear-gradient(300deg, rgb(255, 204, 206), rgb(254, 229, 246))",
  "linear-gradient(300deg, rgb(211, 182, 255), rgb(255, 183, 208))",
];

export const CommentList: React.FC<CommentListProps> = ({ comments }) => {
  const { theme } = useTheme();
  const commentArray = comments.comments;

  // Generate consistent colors for each comment based on user name
  const commentColors = useMemo(() => {
    if (!Array.isArray(commentArray)) return {};

    return commentArray.reduce((acc, comment) => {
      // Use the comment's user name as the seed for color selection
      const colorIndex =
        Math.abs(
          comment.user
            ?.toString()
            .split("")
            .reduce((sum: any, char: string) => sum + char.charCodeAt(0), 0) ||
            0,
        ) % avatarGradients.length;

      acc[comment.id] = avatarGradients[colorIndex];

      return acc;
    }, {});
  }, [commentArray]);

  if (!Array.isArray(commentArray) || commentArray.length === 0) {
    return (
      <div className="text-center py-8 text-default-500">
        <Icon className="mx-auto mb-2 text-3xl" icon="lucide:message-square" />
        <p>No hay comentarios, se el primero!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {commentArray
      // 
      // .sort()
      .map((comment) => (
        <div key={comment.id} className="flex gap-4">
          <Avatar
            className="flex-shrink-0"
            showFallback
            src="https://images.unsplash.com/broken"
            // name={comment.user}
            size="md"
            style={{
              background: commentColors[comment.id],
              backgroundSize: "240% 240%",
              animation: "gradient-animation 10s ease infinite",
            }}
          />
          <div className="flex-grow">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{comment.user}</h3>
              <span className="text-default-400 text-xs">
                {/* 2025-06-23T09:44:07 */}
                {comment.created_at}
              </span>
            </div>
            <p className="text-default-700 mb-3">{comment.text}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
