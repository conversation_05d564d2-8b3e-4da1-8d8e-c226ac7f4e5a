"use client";

import React from "react";
import { Image } from "@heroui/react";

import { useCursorPetStore } from "@/store/use-cursor-pet-store";

export const CursorFollower = () => {
  const [position, setPosition] = React.useState({ x: 0, y: 0 });
  const [isFlipped, setIsFlipped] = React.useState(false);
  const targetRef = React.useRef({ x: 0, y: 0 });
  const prevMousePosRef = React.useRef({ x: 0, y: 0 });
  const { cursorPet } = useCursorPetStore();

  const OFFSET_Y = 30;
  const OFFSET_X = 30;
  const SMOOTHING = 0.15;
  const DIRECTION_CHANGE_THRESHOLD = 3;

  const getPetImage = () => {
    switch (cursorPet) {
      case "froggo":
        return "/broggo_pet.png";
      case "duck":
        return "/duck_pet.png";
      default:
        return null;
    }
  };

  React.useEffect(() => {
    if (cursorPet === "none") return;

    prevMousePosRef.current = { x: 0, y: 0 };

    const handleMouseMove = (e: MouseEvent) => {
      if (
        prevMousePosRef.current.x !== 0 &&
        Math.abs(e.clientX - prevMousePosRef.current.x) >
          DIRECTION_CHANGE_THRESHOLD
      ) {
        const isMovingLeft = e.clientX < prevMousePosRef.current.x;

        setIsFlipped(isMovingLeft);
      }

      const xOffset = isFlipped ? OFFSET_X : -OFFSET_X;

      targetRef.current = {
        x: e.clientX + xOffset,
        y: e.clientY + OFFSET_Y,
      };

      prevMousePosRef.current = { x: e.clientX, y: e.clientY };
    };

    window.addEventListener("mousemove", handleMouseMove);

    let animationFrameId: number;

    const animate = () => {
      setPosition((prev) => ({
        x: prev.x + (targetRef.current.x - prev.x) * SMOOTHING,
        y: prev.y + (targetRef.current.y - prev.y) * SMOOTHING,
      }));

      animationFrameId = requestAnimationFrame(animate);
    };

    animationFrameId = requestAnimationFrame(animate);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      cancelAnimationFrame(animationFrameId);
    };
  }, [isFlipped, cursorPet]);

  if (cursorPet === "none") return null;

  const petImage = getPetImage();

  if (!petImage) return null;

  return (
    <div
      style={{
        position: "fixed",
        left: position.x,
        top: position.y,
        pointerEvents: "none",
        zIndex: 2147483647,
        transform: `translate(-50%, -50%) ${isFlipped ? "" : "scaleX(-1)"}`,
        transition: "transform 0.1s ease-out",
      }}
    >
      <Image
        removeWrapper
        alt="Cursor follower"
        height={50}
        src={petImage}
        width={50}
      />
    </div>
  );
};
