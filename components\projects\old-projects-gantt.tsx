import React, { useEffect, useRef } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  ScrollShadow,
  ButtonGroup,
  Button,
  Card,
  Input,
  DateRangePicker,
  Select,
  SelectItem,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { getStatusStyle, getPhaseStyle } from "../primitives";

import PhaseCountCard from "./phase-count-card";

interface Project {
  id: number;
  name: string;
  status: "en curso" | "prevista" | "on hold" | "cancelado";
  startDate: string; // YYYY-MM-DD
  endDate: string; // YYYY-MM-DD
  completion: number; // Percentage of completion
  phase: string; // Project phase
}

type TimeScale = "day" | "week" | "month";

// Create a common type for time periods that works with all scales
interface TimePeriod {
  key: string;
  label: string;
  width: number;
  tooltip?: string;
  isCurrentDay?: boolean;
  isCurrentWeek?: boolean;
  isCurrentMonth?: boolean;
  monthKey?: string; // For grouping days by month
  startDate?: string; // Start date of week/month
  endDate?: string; // End date of week/month
}

const ProjectsGantt = () => {
  const [timeScale, setTimeScale] = React.useState<TimeScale>("day");
  const [statusFilter, setStatusFilter] = React.useState<string>(""); // New state for status filter
  const [filteredProjects, setFilteredProjects] = React.useState<Project[]>([]); // State for filtered projects

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const todayRef = useRef<HTMLDivElement>(null);

  // Update the sample data to include a phase
  const projects: Project[] = [
    {
      id: 1,
      name: "Project Alpha",
      status: "en curso",
      startDate: "2025-02-03",
      endDate: "2025-03-13",
      completion: 65,
      phase: "START",
    },
    {
      id: 2,
      name: "Project Beta",
      status: "prevista",
      startDate: "2025-01-15",
      endDate: "2025-02-28",
      completion: 100,
      phase: "COLLECTION",
    },
    {
      id: 3,
      name: "Project Gamma",
      status: "on hold",
      startDate: "2025-04-01",
      endDate: "2025-05-15",
      completion: 0,
      phase: "MIGRATION",
    },
  ];

  // Initialize filtered projects on component mount
  useEffect(() => {
    setFilteredProjects(projects);
  }, []);

  // Filter projects when status filter changes
  useEffect(() => {
    if (!statusFilter) {
      setFilteredProjects(projects);

      return;
    }

    const filtered = projects.filter(
      (project) => statusFilter === "" || project.status === statusFilter,
    );

    setFilteredProjects(filtered);
  }, [statusFilter, projects]);

  // Helper function to get ISO week number
  const getWeekNumber = (date: Date) => {
    const target = new Date(date.valueOf());
    const dayNum = (date.getUTCDay() + 6) % 7;

    target.setUTCDate(target.getUTCDate() - dayNum + 3);
    const firstThursday = target.valueOf();

    target.setUTCMonth(0, 1);
    if (target.getUTCDay() !== 4) {
      target.setUTCMonth(0, 1 + ((4 - target.getUTCDay() + 7) % 7));
    }

    return 1 + Math.ceil((firstThursday - target.valueOf()) / 604800000);
  };

  // Format date as DD/MM
  const formatDateDDMM = (dateStr: string) => {
    const date = new Date(dateStr);

    return `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1).toString().padStart(2, "0")}`;
  };

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split("T")[0];
  const todayDate = new Date();

  // Memoize the date generation function to prevent unnecessary recalculations
  const generateDates = React.useCallback(() => {
    const dates = [];

    // Calculate start date (3 months before today)
    const startDate = new Date(todayDate);

    startDate.setMonth(startDate.getMonth() - 6);
    startDate.setDate(1); // Start from the 1st of the month

    // Calculate end date (3 months after today)
    const endDate = new Date(todayDate);

    endDate.setMonth(endDate.getMonth() + 6);
    endDate.setDate(0); // Last day of the month

    // Calculate number of days in the range
    const dayDiff =
      Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
      ) + 1;

    const months: {
      [key: string]: {
        count: number;
        year: number;
        monthName: string;
        startDate: string;
        endDate: string;
      };
    } = {};
    const weeks: {
      [key: string]: {
        count: number;
        year: number;
        weekNum: number;
        monthName: string;
        startDate: string;
        endDate: string;
        dates: string[];
      };
    } = {};

    for (let i = 0; i < dayDiff; i++) {
      const date = new Date(startDate);

      date.setDate(date.getDate() + i);

      const year = date.getFullYear();
      const monthName = date.toLocaleString("default", { month: "short" });
      const monthKey = `${monthName}-${year}`;

      // Get the proper week number
      const weekNum = getWeekNumber(date);
      const weekKey = `${year}-W${weekNum.toString().padStart(2, "0")}`;

      // Day of week
      const dayOfWeek = date.toLocaleString("default", { weekday: "short" });
      const dateStr = date.toISOString().split("T")[0];

      if (!months[monthKey]) {
        months[monthKey] = {
          count: 0,
          year,
          monthName,
          startDate: dateStr,
          endDate: dateStr,
        };
      }
      months[monthKey].count += 1;
      // Always update end date to the latest date in this month
      months[monthKey].endDate = dateStr;

      if (!weeks[weekKey]) {
        weeks[weekKey] = {
          count: 0,
          year,
          weekNum,
          monthName,
          startDate: dateStr,
          endDate: dateStr,
          dates: [],
        };
      }
      weeks[weekKey].count += 1;
      weeks[weekKey].dates.push(dateStr);
      // Always update end date to the latest date in this week
      weeks[weekKey].endDate = dateStr;

      dates.push({
        full: dateStr,
        day: date.getDate(),
        dayOfWeek,
        month: monthName,
        monthKey,
        year,
        week: weekKey,
      });
    }

    return { dates, months, weeks };
  }, [todayDate]);

  // Memoize the result of generateDates to prevent recalculation
  const { dates, months, weeks } = React.useMemo(
    () => generateDates(),
    [generateDates],
  );

  // Find today's period key based on timeScale - memoized to prevent recalculation
  const todayPeriodKey = React.useMemo(() => {
    if (timeScale === "day") {
      return today;
    } else if (timeScale === "week") {
      const weekNum = getWeekNumber(todayDate);
      const year = todayDate.getFullYear();

      return `${year}-W${weekNum.toString().padStart(2, "0")}`;
    } else {
      // Month scale
      const monthName = todayDate.toLocaleString("default", { month: "short" });
      const year = todayDate.getFullYear();

      return `${monthName}-${year}`;
    }
  }, [timeScale, today, todayDate]);

  // Create memoized button click handlers to prevent unnecessary rerenders
  const handleDayClick = React.useCallback(() => setTimeScale("day"), []);
  const handleWeekClick = React.useCallback(() => setTimeScale("week"), []);
  const handleMonthClick = React.useCallback(() => setTimeScale("month"), []);

  // Find the relative position of today within its month or week (0-1 range)
  const todayPositionInPeriod = React.useMemo(() => {
    if (timeScale === "day") return 0;

    if (timeScale === "week") {
      // Find today's week
      const todayWeekNum = getWeekNumber(todayDate);
      const todayYear = todayDate.getFullYear();
      const todayWeekKey = `${todayYear}-W${todayWeekNum.toString().padStart(2, "0")}`;

      // Find all dates in this week
      const datesInThisWeek = dates.filter((date) => {
        const weekNum = getWeekNumber(new Date(date.full));
        const year = new Date(date.full).getFullYear();
        const weekKey = `${year}-W${weekNum.toString().padStart(2, "0")}`;

        return weekKey === todayWeekKey;
      });

      if (datesInThisWeek.length === 0) return 0;

      // Find position of today in the week's dates
      const todayIndex = datesInThisWeek.findIndex(
        (date) => date.full === today,
      );

      // Calculate relative position (0 to 1)
      return todayIndex >= 0 ? todayIndex / (datesInThisWeek.length - 1) : 0;
    } else if (timeScale === "month") {
      // Find today's month
      const todayMonth = todayDate.toLocaleString("default", {
        month: "short",
      });
      const todayYear = todayDate.getFullYear();
      const todayMonthKey = `${todayMonth}-${todayYear}`;

      // Find all dates in this month
      const datesInThisMonth = dates.filter((date) => {
        return date.monthKey === todayMonthKey;
      });

      if (datesInThisMonth.length === 0) return 0;

      // Find position of today in the month's dates
      const todayIndex = datesInThisMonth.findIndex(
        (date) => date.full === today,
      );

      // Calculate relative position (0 to 1)
      return todayIndex >= 0 ? todayIndex / (datesInThisMonth.length - 1) : 0;
    }

    return 0;
  }, [timeScale, todayDate, dates, today]);

  // Focus on today's date when component mounts or timeScale changes
  useEffect(() => {
    if (todayRef.current && scrollContainerRef.current) {
      // Add a small delay to make sure the DOM is fully rendered
      setTimeout(() => {
        const containerWidth = scrollContainerRef.current?.clientWidth || 0;
        const todayPosition = todayRef.current?.offsetLeft || 0;

        // Center today's marker in the viewport
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollLeft =
            todayPosition - containerWidth / 2;
        }
      }, 100);
    }
  }, [timeScale]);

  // Function to check if a date is within project timeline
  const isDateInProject = (project: Project, date: string) => {
    return date >= project.startDate && date <= project.endDate;
  };

  // Function to check if a date is the start or end of a project
  const isProjectBoundary = (
    project: Project,
    date: string,
    type: "start" | "end",
  ) => {
    return type === "start"
      ? date === project.startDate
      : date === project.endDate;
  };

  // Calculate project timeline for non-daily views
  const getProjectTimelinePosition = React.useMemo(() => {
    return (project: Project, timePeriods: TimePeriod[]) => {
      if (timeScale === "day") return null;

      // Find project start and end positions across all periods
      let startPeriodIndex = -1;
      let endPeriodIndex = -1;
      let startOffsetInPeriod = 0;
      let endOffsetInPeriod = 0;

      // For week or month view, we need to find which time periods contain the start and end dates
      for (let i = 0; i < timePeriods.length; i++) {
        const period = timePeriods[i];

        // Check if this period contains the start date
        if (startPeriodIndex === -1) {
          const periodDates =
            timeScale === "week"
              ? dates.filter((d) => d.week === period.key)
              : dates.filter((d) => d.monthKey === period.key);

          if (periodDates.some((d) => d.full >= project.startDate)) {
            startPeriodIndex = i;

            // Calculate offset within the period (0-1) based on the exact day
            const projectStartDate = new Date(project.startDate);
            const periodStartDate = new Date(periodDates[0].full);
            const periodEndDate = new Date(
              periodDates[periodDates.length - 1].full,
            );

            if (projectStartDate > periodStartDate) {
              const totalDays =
                (periodEndDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24) + 1; // Add 1 to include both start and end days
              const daysFromStart =
                (projectStartDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24);

              startOffsetInPeriod = Math.max(
                0,
                Math.min(1, daysFromStart / totalDays),
              );
            }
          }
        }

        // Check if this period contains the end date
        if (endPeriodIndex === -1 && startPeriodIndex !== -1) {
          const periodDates =
            timeScale === "week"
              ? dates.filter((d) => d.week === period.key)
              : dates.filter((d) => d.monthKey === period.key);

          if (periodDates.some((d) => d.full >= project.endDate)) {
            endPeriodIndex = i;

            // Calculate offset within the period (0-1) based on the exact day
            const projectEndDate = new Date(project.endDate);
            const periodStartDate = new Date(periodDates[0].full);
            const periodEndDate = new Date(
              periodDates[periodDates.length - 1].full,
            );

            if (projectEndDate < periodEndDate) {
              const totalDays =
                (periodEndDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24) + 1; // Add 1 to include both start and end days
              const daysFromStart =
                (projectEndDate.getTime() - periodStartDate.getTime()) /
                (1000 * 60 * 60 * 24);

              endOffsetInPeriod = Math.max(
                0,
                Math.min(1, daysFromStart / totalDays),
              );
            } else {
              endOffsetInPeriod = 1;
            }
          }
        }

        // If we've found both start and end, we can stop
        if (startPeriodIndex !== -1 && endPeriodIndex !== -1) break;
      }

      // If the end date goes beyond our visible range
      if (startPeriodIndex !== -1 && endPeriodIndex === -1) {
        endPeriodIndex = timePeriods.length - 1;
        endOffsetInPeriod = 1;
      }

      return {
        startPeriodIndex,
        endPeriodIndex,
        startOffsetInPeriod,
        endOffsetInPeriod,
      };
    };
  }, [timeScale, dates]);

  // Get time periods based on selected scale - optimized with memoization
  const timePeriods = React.useMemo((): TimePeriod[] => {
    switch (timeScale) {
      case "month":
        return Object.entries(months).map(
          ([monthKey, { monthName, startDate, endDate }]) => ({
            key: monthKey,
            label: monthName,
            width: 120,
            isCurrentMonth: monthKey === todayPeriodKey,
            startDate,
            endDate,
          }),
        );

      case "week":
        return Object.entries(weeks).map(
          ([weekKey, { weekNum, startDate, endDate }]) => ({
            key: weekKey,
            label: `${weekNum}`,
            tooltip: `Week ${weekNum}`,
            width: 60,
            isCurrentWeek: weekKey === todayPeriodKey,
            startDate,
            endDate,
          }),
        );

      default: // day view
        return dates.map(({ full, day, dayOfWeek, monthKey }) => ({
          key: full,
          label: `${day}`,
          tooltip: `${dayOfWeek} ${day}`,
          width: 32,
          isCurrentDay: full === today,
          monthKey,
        }));
    }
  }, [timeScale, months, weeks, dates, today, todayPeriodKey]);

  // Group days by month for day view
  const groupedByMonth = React.useMemo(() => {
    if (timeScale !== "day") return null;

    const groups: { [key: string]: TimePeriod[] } = {};

    timePeriods.forEach((period) => {
      if (!period.monthKey) return;

      if (!groups[period.monthKey]) {
        groups[period.monthKey] = [];
      }

      groups[period.monthKey].push(period);
    });

    return groups;
  }, [timePeriods, timeScale]);

  return (
    <div className="w-full pt-4">
      <Card className="w-full p-2 mb-4 pb-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por LID, ALIAS o IMPLEMENTADOR... (◕‿◕✿)"
              startContent={"🔍"}
              // value={searchTerm}
              // onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <DateRangePicker
              showMonthAndYearPickers
              className="w-min"
              // value={dateRange}
              visibleMonths={2}
              // onChange={(value) => setDateRange(value)}
            />

            <Button
              // color={
              //   Object.keys(activeFilters).length > 0 || dateRange || searchTerm
              //     ? "primary"
              //     : "default"
              // }
              variant="flat"
              // onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {/* {(Object.keys(activeFilters).length > 0 ||
                dateRange ||
                searchTerm) &&
                `(${
                  Object.keys(activeFilters).length +
                  (dateRange ? 1 : 0) +
                  (searchTerm ? 1 : 0)
                })`} */}
            </Button>
          </div>
        </div>
        <div className={"w-full flex flex-col items-end"}>
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <ButtonGroup variant="flat">
              <Button
                color={timeScale === "day" ? "primary" : "default"}
                size="sm"
                onPress={handleDayClick}
              >
                <Icon className="w-4 h-4 mr-1" icon="lucide:calendar-days" />
                Dias
              </Button>
              <Button
                color={timeScale === "week" ? "primary" : "default"}
                size="sm"
                onPress={handleWeekClick}
              >
                <Icon className="w-4 h-4 mr-1" icon="lucide:calendar-range" />
                Semanas
              </Button>
              <Button
                color={timeScale === "month" ? "primary" : "default"}
                size="sm"
                onPress={handleMonthClick}
              >
                <Icon className="w-4 h-4 mr-1" icon="lucide:calendar" />
                Meses
              </Button>
            </ButtonGroup>
          </div>
        </div>
        <div className={"w-full flex flex-col items-center"}>
          <div className="w-7/12 flex flex-col sm:flex-row gap-4 justify-between items-center">
            <PhaseCountCard phase="START" projects={[]} />
            <PhaseCountCard phase="COLLECTION" projects={[]} />
            <PhaseCountCard phase="MIGRATION" projects={[]} />
            <PhaseCountCard phase="TEST" projects={[]} />
            <PhaseCountCard phase="GO LIVE" projects={[]} />
            <PhaseCountCard phase="INCUBADORA" projects={[]} />
            <PhaseCountCard phase="TOTAL" projects={[]} />
          </div>
        </div>
      </Card>

      <div className="flex">
        {/* Left side - Project info columns */}
        <div className="w-1/3 border-r">
          <Table
            removeWrapper
            aria-label="Project information"
            className="min-w-full"
          >
            <TableHeader>
              <TableColumn>LID</TableColumn>
              <TableColumn>Alias</TableColumn>
              <TableColumn>Implementador</TableColumn>
              <TableColumn>Estado</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredProjects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell>{project.id}</TableCell>
                  <TableCell>{project.name}</TableCell>
                  <TableCell>{project.name}</TableCell>
                  <TableCell>
                    <span className={`${getStatusStyle(project.status)}`}>
                      {project.status}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Right side - Gantt chart */}
        <div className="w-2/3">
          <ScrollShadow
            ref={scrollContainerRef}
            hideScrollBar
            className="w-full overflow-x-auto"
          >
            <div className="flex min-w-max">
              <div className="flex flex-col w-full relative">
                {timeScale === "day" && groupedByMonth ? (
                  /* Day mode with month grouping */
                  <div className="flex flex-col">
                    <div className="flex border-b border-default-200">
                      {/* Month headers */}
                      {Object.entries(groupedByMonth).map(
                        ([monthKey, days]) => (
                          <div
                            key={monthKey}
                            className="flex-shrink-0 text-xs font-medium text-center border-r border-default-200 bg-default-50"
                            style={{
                              width: `${days.length * 32}px`,
                            }}
                          >
                            {monthKey}
                          </div>
                        ),
                      )}
                    </div>

                    {/* Day headers */}
                    <div className="flex border-b border-default-200">
                      {timePeriods.map((period) => (
                        <div
                          key={period.key}
                          ref={period.key === todayPeriodKey ? todayRef : null}
                          className={`flex-shrink-0 text-xs font-medium text-center border-r border-default-200 align-middle ${
                            period.key === todayPeriodKey ? "bg-red-50" : ""
                          } relative`}
                          style={{
                            width: "32px",
                            height: "22px",
                          }}
                        >
                          {period.label}
                          <div className="text-[8px] text-default-500">
                            {/* {period.tooltip?.split(" ")[0]} */}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Gantt rows */}
                    <div className="w-full">
                      {filteredProjects.map((project) => (
                        <div
                          key={project.id}
                          className="h-[37px] flex border-b border-default-200 relative"
                        >
                          {dates.map(({ full }) => {
                            const isInProject = isDateInProject(project, full);
                            const isStart = isProjectBoundary(
                              project,
                              full,
                              "start",
                            );
                            const isEnd = isProjectBoundary(
                              project,
                              full,
                              "end",
                            );

                            return (
                              <div
                                key={full}
                                className={`w-8 flex-shrink-0 h-full flex items-center justify-center ${full === today ? "bg-red-50" : ""}`}
                              >
                                {isInProject && (
                                  <div
                                    className={`h-6 w-full flex items-center justify-center
                                      ${isStart ? "rounded-l-lg" : ""}
                                      ${isEnd ? "rounded-r-lg" : ""}
                                      ${!isStart && !isEnd ? "border-l-0 border-r-0" : ""}
                                      ${getPhaseStyle(project.phase)}
                                    `}
                                  >
                                    {isStart && (
                                      <div className="absolute left-0 w-1 h-6 bg-gray-500" />
                                    )}
                                    {isEnd && (
                                      <div className="absolute right-0 w-1 h-6 bg-gray-500" />
                                    )}
                                    {project.completion === 100 && (
                                      <Icon
                                        className={`w-3 h-3 text-${getPhaseStyle(project.phase)}`}
                                        icon="lucide:check"
                                      />
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  /* Week or Month mode */
                  <div className="flex flex-col">
                    <div className="flex border-b border-default-200">
                      {timePeriods.map((period) => (
                        <div
                          key={period.key}
                          ref={period.key === todayPeriodKey ? todayRef : null}
                          className={`flex-shrink-0 text-xs font-medium text-center border-r border-default-200 align-middle ${
                            period.key === todayPeriodKey ? "bg-red-50" : ""
                          } relative`}
                          style={{
                            width: `${period.width}px`,
                            height: timeScale === "week" ? "40px" : "40px", // Taller for week to fit date range
                          }}
                        >
                          {period.label}

                          {/* Show date range for week view */}
                          {timeScale === "week" &&
                            period.startDate &&
                            period.endDate && (
                              <div className="text-[8px] text-default-500">
                                {formatDateDDMM(period.startDate)} -{" "}
                                {formatDateDDMM(period.endDate)}
                              </div>
                            )}
                        </div>
                      ))}
                    </div>

                    {/* Gantt rows */}
                    <div className="w-full">
                      {filteredProjects.map((project) => {
                        // Calculate project timeline positions for week/month views
                        const timelinePositions = getProjectTimelinePosition(
                          project,
                          timePeriods,
                        );

                        return (
                          <div
                            key={project.id}
                            className="h-[37px] flex border-b border-default-200 relative"
                          >
                            {/* For week/month views, draw a continuous bar across periods */}
                            <>
                              {timelinePositions &&
                                timelinePositions.startPeriodIndex !== -1 && (
                                  <div
                                    className={`absolute h-6 ${getPhaseStyle(project.phase)} rounded-lg z-10 top-[13px] border`}
                                    style={{
                                      left: `${
                                        timelinePositions.startPeriodIndex > 0
                                          ? timePeriods
                                              .slice(
                                                0,
                                                timelinePositions.startPeriodIndex,
                                              )
                                              .reduce(
                                                (sum, p) => sum + p.width,
                                                0,
                                              ) +
                                            timePeriods[
                                              timelinePositions.startPeriodIndex
                                            ].width *
                                              timelinePositions.startOffsetInPeriod
                                          : timePeriods[0].width *
                                            timelinePositions.startOffsetInPeriod
                                      }px`,
                                      width: `${(() => {
                                        // Calculate total width of all periods between start and end
                                        const startPos =
                                          timelinePositions.startPeriodIndex > 0
                                            ? timePeriods
                                                .slice(
                                                  0,
                                                  timelinePositions.startPeriodIndex,
                                                )
                                                .reduce(
                                                  (sum, p) => sum + p.width,
                                                  0,
                                                ) +
                                              timePeriods[
                                                timelinePositions
                                                  .startPeriodIndex
                                              ].width *
                                                timelinePositions.startOffsetInPeriod
                                            : timePeriods[0].width *
                                              timelinePositions.startOffsetInPeriod;

                                        const endPos =
                                          timelinePositions.endPeriodIndex >= 0
                                            ? timePeriods
                                                .slice(
                                                  0,
                                                  timelinePositions.endPeriodIndex,
                                                )
                                                .reduce(
                                                  (sum, p) => sum + p.width,
                                                  0,
                                                ) +
                                              timePeriods[
                                                timelinePositions.endPeriodIndex
                                              ].width *
                                                timelinePositions.endOffsetInPeriod
                                            : 0;

                                        return endPos - startPos;
                                      })()}px`,
                                    }}
                                  >
                                    {project.completion === 100 && (
                                      <div className="h-full flex items-center justify-center">
                                        <Icon
                                          className={`w-3 h-3 ${getPhaseStyle(project.phase)}`}
                                          icon="lucide:check"
                                        />
                                      </div>
                                    )}
                                    {project.completion !== undefined &&
                                      project.completion > 0 &&
                                      project.completion < 100 && (
                                        <div
                                          className={`h-full ${getPhaseStyle(project.phase)} rounded-l-lg`}
                                          style={{
                                            width: `${project.completion}%`,
                                          }}
                                        />
                                      )}
                                  </div>
                                )}

                              {/* Render time period cells as background */}
                              {timePeriods.map((period) => (
                                <div
                                  key={period.key}
                                  className={`flex-shrink-0 h-full border-r border-default-200 ${period.key === todayPeriodKey ? "bg-red-50" : ""} relative`}
                                  style={{ width: `${period.width}px` }}
                                >
                                  {/* Thin vertical line for current day in week or month view */}
                                  {((timeScale === "week" &&
                                    period.isCurrentWeek) ||
                                    (timeScale === "month" &&
                                      period.isCurrentMonth)) && (
                                    <div
                                      className="absolute top-0 w-px h-full bg-red-500"
                                      style={{
                                        left: `${todayPositionInPeriod * 100}%`,
                                        zIndex: 10,
                                      }}
                                    />
                                  )}
                                </div>
                              ))}
                            </>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </ScrollShadow>
        </div>
      </div>

      <div className="mt-4 text-right text-sm text-default-500">
        (っ˘ω˘ς ) Scroll horizontally to see more dates!
      </div>
    </div>
  );
};

export default ProjectsGantt;
