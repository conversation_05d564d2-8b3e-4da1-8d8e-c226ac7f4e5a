"use client";

import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/table";
import { Button } from "@heroui/button";
import { Icon } from "@iconify/react";
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Form,
  Input,
  Select,
  SelectItem,
  Spinner,
} from "@heroui/react";
import { useQuery } from "@apollo/client";
import { useMutation } from "@apollo/client";

import {
  AllPhasesDocument,
  AllSubphasesDocument,
  ExecuteCreateSubphaseDocument,
  ExecuteUpdateSubphaseDocument,
  ExecuteDeleteSubphaseDocument,
  SubphaseInput,
} from "@/graphql/schemas/generated";
import { useSubphaseFields } from "@/hooks/fields/useSubphaseFields";
import { useSubphaseOrder } from "@/hooks/subphase/useSubphaseOrder";

interface SubphaseProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
  canEditConfiguration: boolean;
}

// Define a type for subphases
interface Subphase {
  id: string;
  name: string;
  order: number;
  phase: {
    id: string;
    name: string;
  };
  fieldSet?: Array<any>; // Add this to track associated fields count
}

// Component for the Add Subphase Modal
interface AddSubphaseData {
  name: string;
  phase: string;
}

function SubphaseModal({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AddSubphaseData) => void;
}) {
  const { data: phases } = useQuery(AllPhasesDocument);

  const [createSubphase, { loading: loadingCreateSubphase }] = useMutation<
    { createSubphase: { subphase: { name: string; order: number } } },
    { input: SubphaseInput }
  >(ExecuteCreateSubphaseDocument);

  const [formData, setFormData] = React.useState<AddSubphaseData>({
    name: "",
    phase: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);

    // Execute the mutation to create a new subphase
    if (formData.name && formData.phase) {
      createSubphase({
        variables: {
          input: {
            name: formData.name,
            phaseId: formData.phase,
          },
        },
        onCompleted: () => {
          setFormData({ name: "", phase: "" });
          onClose();
        },
        onError: () => {
          // Error handling - could show a toast notification here
        },
        refetchQueries: [{ query: AllSubphasesDocument }],
      });
    }
  };

  const phaseOptions =
    phases?.allPhases?.map((phase: { id: any; name: any }) => ({
      key: phase?.id || "",
      label: phase?.name || "",
    })) || [];

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <Form className="w-full" onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Añadir subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <Input
                isRequired
                className="w-full"
                label="Nombre de la subfase"
                labelPlacement="outside"
                placeholder="Insertar nombre de la subfase"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:text"
                  />
                }
                value={formData.name}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, name: value }))
                }
              />

              <Select
                isRequired
                className="w-full"
                label="Fase"
                labelPlacement="outside"
                placeholder="Seleccionar fase"
                selectedKeys={formData.phase ? [formData.phase] : []}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    phase: e.target.value,
                  }));
                }}
              >
                {phaseOptions.map(
                  (phase: {
                    key: React.Key | null | undefined;
                    label:
                      | string
                      | number
                      | bigint
                      | boolean
                      | React.ReactElement<
                          any,
                          string | React.JSXElementConstructor<any>
                        >
                      | Iterable<React.ReactNode>
                      | React.ReactPortal
                      | Promise<React.AwaitedReactNode>
                      | null
                      | undefined;
                  }) => (
                    <SelectItem key={phase.key}>
                      {typeof phase.label === "string"
                        ? phase.label.toLowerCase() === "incubadora"
                          ? "TAKE OFF"
                          : phase.label
                        : "Sin nombre"}
                    </SelectItem>
                  ),
                )}
              </Select>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                isLoading={loadingCreateSubphase}
                type="submit"
              >
                {loadingCreateSubphase ? "Añadiendo..." : "Añadir"}
              </Button>
            </ModalFooter>
          </Form>
        )}
      </ModalContent>
    </Modal>
  );
}

// Component for the Edit Subphase Modal
function EditSubphaseModal({
  isOpen,
  onClose,
  onConfirm,
  initialData,
  subphaseId,
  hasAssociatedFields,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AddSubphaseData) => void;
  initialData: AddSubphaseData;
  subphaseId: string;
  hasAssociatedFields: boolean;
}) {
  const { data: phases } = useQuery(AllPhasesDocument);

  const [updateSubphase, { loading: loadingUpdateSubphase }] = useMutation(
    ExecuteUpdateSubphaseDocument,
  );

  const [formData, setFormData] = React.useState<AddSubphaseData>(initialData);

  // Reset form data when initialData changes or modal opens
  React.useEffect(() => {
    if (isOpen) {
      setFormData(initialData);
    }
  }, [initialData, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);

    // Execute the mutation to update the subphase
    if (formData.name && formData.phase) {
      updateSubphase({
        variables: {
          id: parseInt(subphaseId), // Use the actual subphase ID
          input: {
            name: formData.name,
            phaseId: formData.phase,
          },
        },
        onCompleted: () => {
          onClose();
        },
        onError: () => {
          // Error handling - could show a toast notification here
        },
        refetchQueries: [{ query: AllSubphasesDocument }],
      });
    }
  };

  const phaseOptions =
    phases?.allPhases?.map((phase: { id: any; name: any }) => ({
      key: phase?.id || "",
      label: phase?.name || "",
    })) || [];

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <Form className="w-full" onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Editar subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <Input
                isRequired
                className="w-full"
                label="Nombre de la subfase"
                labelPlacement="outside"
                placeholder="Insertar nombre de la subfase"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:text"
                  />
                }
                value={formData.name}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, name: value }))
                }
              />

              <Select
                isRequired
                className="w-full"
                isDisabled={hasAssociatedFields}
                label="Fase"
                labelPlacement="outside"
                placeholder={
                  hasAssociatedFields
                    ? "No se puede cambiar (tiene campos asociados)"
                    : "Seleccionar fase"
                }
                selectedKeys={formData.phase ? [formData.phase] : []}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    phase: e.target.value,
                  }));
                }}
              >
                {phaseOptions.map(
                  (phase: {
                    key: React.Key | null | undefined;
                    label:
                      | string
                      | number
                      | bigint
                      | boolean
                      | React.ReactElement<
                          any,
                          string | React.JSXElementConstructor<any>
                        >
                      | Iterable<React.ReactNode>
                      | React.ReactPortal
                      | Promise<React.AwaitedReactNode>
                      | null
                      | undefined;
                  }) => (
                    <SelectItem key={phase.key}>
                      {typeof phase.label === "string"
                        ? phase.label.toLowerCase() === "incubadora"
                          ? "TAKE OFF"
                          : phase.label
                        : "Sin nombre"}
                    </SelectItem>
                  ),
                )}
              </Select>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                isLoading={loadingUpdateSubphase}
                type="submit"
              >
                {loadingUpdateSubphase ? "Actualizando..." : "Actualizar"}
              </Button>
            </ModalFooter>
          </Form>
        )}
      </ModalContent>
    </Modal>
  );
}

// Component for the Delete Subphase Modal
function DeleteSubphaseModal({
  isOpen,
  onClose,
  onConfirm,
  subphaseId,
  subphaseName,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  subphaseId: string;
  subphaseName: string;
}) {
  const [deleteSubphase, { loading: loadingDeleteSubphase }] = useMutation(
    ExecuteDeleteSubphaseDocument,
  );

  const handleConfirm = () => {
    if (subphaseId) {
      deleteSubphase({
        variables: {
          id: parseInt(subphaseId),
        },
        onCompleted: () => {
          onConfirm();
          onClose();
        },
        onError: () => {
          // Error handling - could show a toast notification here
        },
        refetchQueries: [{ query: AllSubphasesDocument }],
      });
    }
  };

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Eliminar subfase
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <p>
                ¿Estás seguro de que deseas eliminar la subfase &quot;
                {subphaseName}&quot;?
              </p>
              <p className="text-sm text-gray-500">
                Esta acción no se puede deshacer.
              </p>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="danger"
                isLoading={loadingDeleteSubphase}
                onPress={handleConfirm}
              >
                {loadingDeleteSubphase ? "Eliminando..." : "Eliminar"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

export default function Subphase({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: SubphaseProps) {
  const { decreaseSubphaseOrder, increaseSubphaseOrder } = useSubphaseOrder();

  const [isOpen, setIsOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [movingSubphaseId, setMovingSubphaseId] = useState<number | null>(null);
  const [selectedSubphase, setSelectedSubphase] = useState<{
    id: string;
    name: string;
    phase: string;
  }>({
    id: "",
    name: "",
    phase: "",
  });

  const {
    loading: loadingSubphases,
    error: errorSubphases,
    data: subphases,
    refetch: refetchSubphases,
  } = useQuery(AllSubphasesDocument);

  const {
    loading: loadingPhases,
    error: errorPhases,
    data: phases,
  } = useQuery(AllPhasesDocument);

  // Use the custom hook to get subphase fields data
  const {
    loading: loadingSubphaseFields,
    error: errorSubphaseFields,
    fetchSubphaseFields,
    getFieldsCountBySubphaseId,
    hasFields,
  } = useSubphaseFields();

  // Group and sort subphases by phase order
  const sortedSubphasesByPhase: Array<{
    phaseName: string;
    phaseOrder: number;
    subphases: Subphase[];
  }> = React.useMemo(() => {
    if (!subphases?.allSubphases || !phases?.allPhases) {
      return [];
    }

    // Create a map of phase ID to phase order
    const phaseOrderMap = new Map<string, { name: string; order: number }>();

    phases.allPhases.forEach((phase: { id: string; name: any; order: any }) => {
      if (phase) {
        phaseOrderMap.set(phase.id, { name: phase.name, order: phase.order });
      }
    });

    // Group subphases by phase
    const groupedByPhase: Record<string, Subphase[]> = {};

    subphases.allSubphases.forEach((subphase: Subphase) => {
      if (subphase) {
        const phaseName = subphase.phase.name;

        if (!groupedByPhase[phaseName]) {
          groupedByPhase[phaseName] = [];
        }
        groupedByPhase[phaseName].push(subphase as Subphase);
      }
    });

    // Convert to array and sort by phase order
    const result = Object.entries(groupedByPhase).map(
      ([phaseName, subphaseList]) => {
        // Find the phase order by matching phase name
        const phaseInfo = Array.from(phaseOrderMap.values()).find(
          (p) => p.name === phaseName,
        );

        return {
          phaseName,
          phaseOrder: phaseInfo?.order || 999, // Default high order for unknown phases
          subphases: subphaseList.sort((a, b) => a.order - b.order), // Sort subphases by order
        };
      },
    );

    // Sort by phase order
    return result.sort((a, b) => a.phaseOrder - b.phaseOrder);
  }, [subphases, phases]);

  // Fetch subphase fields data when component mounts
  useEffect(() => {
    fetchSubphaseFields();
  }, []);

  useEffect(() => {
    if (isCreating) {
      setIsOpen(true);
      setIsCreating(false);
    }
  }, [isCreating, setIsCreating]);

  const handleModalConfirm = (_data: AddSubphaseData) => {
    fetchSubphaseFields();
  };

  const handleEditSubphase = (subphase: Subphase) => {
    setSelectedSubphase({
      id: subphase.id,
      name: subphase.name,
      phase: subphase.phase.id,
    });
    setIsEditModalOpen(true);
  };

  const handleEditConfirm = (_data: AddSubphaseData) => {
    // This will be handled by the EditSubphaseModal component
    setIsEditModalOpen(false);
    // Refetch subphase fields to update counts
    fetchSubphaseFields();
  };

  const handleDeleteSubphase = (subphase: Subphase) => {
    setSelectedSubphase({
      id: subphase.id,
      name: subphase.name,
      phase: subphase.phase.id,
    });
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = () => {
    // This will be handled by the DeleteSubphaseModal component
    setIsDeleteModalOpen(false);
    // Refetch subphase fields to update counts
    fetchSubphaseFields();
  };

  const handleMoveSubphase = async (
    direction: "up" | "down",
    subphaseId: number,
  ) => {
    try {
      setMovingSubphaseId(subphaseId);
      let result;

      if (direction === "up") {
        result = await increaseSubphaseOrder(subphaseId);
      } else {
        result = await decreaseSubphaseOrder(subphaseId);
      }

      if (result.success) {
        // Refetch subphases to get updated order
        await refetchSubphases();
        // Also refetch subphase fields to update counts
        fetchSubphaseFields();
      }
    } catch {
      // Error handling - could show a toast notification here
    } finally {
      setMovingSubphaseId(null);
    }
  };

  // Function to render a subphase table for a specific phase
  const renderSubphaseTable = (phaseName: string, subphaseList: Subphase[]) => {
    return (
      <div key={phaseName} className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-2xl font-semibold">
            {phaseName.toLowerCase() === "incubadora" ? "TAKE OFF" : phaseName}
          </h2>
        </div>
        <div>
          <Table removeWrapper aria-label={`${phaseName} subphases table`}>
            <TableHeader>
              <TableColumn>#</TableColumn>
              <TableColumn>Subfase</TableColumn>
              <TableColumn>Campos asociados</TableColumn>
              <TableColumn className="text-center">Acciones</TableColumn>
            </TableHeader>
            <TableBody>
              {subphaseList.map((subphase, index) => {
                const subphaseIdNum = parseInt(subphase.id);
                const isMoving = movingSubphaseId === subphaseIdNum;
                const isFirstInPhase = index === 0;
                const isLastInPhase = index === subphaseList.length - 1;

                return (
                  <TableRow key={subphase.id}>
                    <TableCell>{subphase.order}</TableCell>
                    <TableCell>{subphase.name}</TableCell>
                    <TableCell>
                      {getFieldsCountBySubphaseId(subphaseIdNum)}
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-center gap-2">
                        <Button
                          isIconOnly
                          isDisabled={
                            isFirstInPhase || isMoving || !canEditConfiguration
                          }
                          isLoading={isMoving}
                          size="sm"
                          variant="light"
                          onPress={() =>
                            handleMoveSubphase("up", subphaseIdNum)
                          }
                        >
                          {!isMoving && (
                            <Icon
                              className="text-lg"
                              icon="lucide:chevron-up"
                            />
                          )}
                        </Button>
                        <Button
                          isIconOnly
                          isDisabled={
                            isLastInPhase || isMoving || !canEditConfiguration
                          }
                          isLoading={isMoving}
                          size="sm"
                          variant="light"
                          onPress={() =>
                            handleMoveSubphase("down", subphaseIdNum)
                          }
                        >
                          {!isMoving && (
                            <Icon
                              className="text-lg"
                              icon="lucide:chevron-down"
                            />
                          )}
                        </Button>
                        <Button
                          isIconOnly
                          color="primary"
                          isDisabled={isMoving || !canEditConfiguration}
                          size="sm"
                          variant="flat"
                          onPress={() => handleEditSubphase(subphase)}
                        >
                          <Icon className="text-lg" icon="lucide:edit-3" />
                        </Button>
                        <Button
                          isIconOnly
                          color="danger"
                          isDisabled={
                            hasFields(subphaseIdNum) ||
                            isMoving ||
                            !canEditConfiguration
                          }
                          size="sm"
                          variant="flat"
                          onPress={() => handleDeleteSubphase(subphase)}
                        >
                          <Icon className="text-lg" icon="lucide:trash-2" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };

  if (
    loadingSubphases &&
    loadingPhases &&
    loadingSubphaseFields &&
    !subphases
  ) {
    return (
      <div className="w-full flex justify-center items-center p-6">
        <Spinner label="Cargando subfases..." />
      </div>
    );
  }

  if (errorSubphases || errorPhases || errorSubphaseFields) {
    return <div>Error loading data</div>;
  }

  return (
    <div className="pt-4 w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {sortedSubphasesByPhase.map(({ phaseName, subphases }) =>
          renderSubphaseTable(phaseName, subphases),
        )}
      </div>
      <SubphaseModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={handleModalConfirm}
      />
      <EditSubphaseModal
        hasAssociatedFields={hasFields(parseInt(selectedSubphase.id))}
        initialData={{
          name: selectedSubphase.name,
          phase: selectedSubphase.phase,
        }}
        isOpen={isEditModalOpen}
        subphaseId={selectedSubphase.id}
        onClose={() => setIsEditModalOpen(false)}
        onConfirm={handleEditConfirm}
      />
      <DeleteSubphaseModal
        isOpen={isDeleteModalOpen}
        subphaseId={selectedSubphase.id}
        subphaseName={selectedSubphase.name}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}
