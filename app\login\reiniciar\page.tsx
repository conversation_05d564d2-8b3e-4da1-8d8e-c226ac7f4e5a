"use client";

import React from "react";
import { Input, <PERSON><PERSON>, Card, Spacer } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useSearchParams } from "next/navigation";

import { useAuth } from "@/hooks/auth/useAuth";

export default function NewPasswordPage() {
  const { resetPassword } = useAuth();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [password, setPassword] = React.useState("");
  const [confirmPassword, setConfirmPassword] = React.useState("");
  const [isVisible, setIsVisible] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const [error, setError] = React.useState("");

  const toggleVisibility = () => setIsVisible(!isVisible);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token) {
      setError("Token de restablecimiento no encontrado en la URL");

      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Use the token from the URL
      await resetPassword(password);
      setIsSuccess(true);
    } catch (err: any) {
      setError(err.message || "Error al restablecer la contraseña");
    } finally {
      setIsLoading(false);
    }
  };

  // Show error if no token is found
  React.useEffect(() => {
    if (!token && !isSuccess) {
      setError("Token de restablecimiento no encontrado en la URL");
    }
  }, [token, isSuccess]);

  return (
    <div className="min-h-screen w-full flex items-center justify-center">
      <Card className="w-full max-w-md p-6 space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">
            {isSuccess ? "¡Todo listo! (★ω★)" : "Crear nueva contraseña"}
          </h1>
          <p className="text-default-500">
            {isSuccess
              ? "¡Tu contraseña ha sido actualizada exitosamente!"
              : "Por favor, ingresa tu nueva contraseña"}
          </p>
        </div>

        {error && (
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"
            role="alert"
          >
            <p>{error}</p>
          </div>
        )}

        {!isSuccess ? (
          <form className="space-y-4" onSubmit={handleSubmit}>
            <Input
              isRequired
              endContent={
                <button type="button" onClick={toggleVisibility}>
                  <Icon
                    className="text-default-400"
                    icon={isVisible ? "lucide:eye" : "lucide:eye-off"}
                    width={20}
                  />
                </button>
              }
              label="Nueva contraseña"
              placeholder="Ingresa nueva contraseña"
              startContent={
                <Icon
                  className="text-default-400"
                  icon="lucide:lock"
                  width={20}
                />
              }
              type={isVisible ? "text" : "password"}
              value={password}
              onValueChange={setPassword}
            />

            <Input
              isRequired
              label="Confirmar contraseña"
              placeholder="Confirma tu contraseña"
              startContent={
                <Icon
                  className="text-default-400"
                  icon="lucide:lock"
                  width={20}
                />
              }
              type={isVisible ? "text" : "password"}
              value={confirmPassword}
              onValueChange={setConfirmPassword}
            />

            <Spacer y={2} />

            <Button
              fullWidth
              color="primary"
              isDisabled={password !== confirmPassword || !password || !token}
              isLoading={isLoading}
              size="lg"
              type="submit"
            >
              Actualizar contraseña
            </Button>
          </form>
        ) : (
          <Button
            fullWidth
            color="primary"
            onClick={() => (window.location.href = "/login")}
          >
            Volver a iniciar sesión
          </Button>
        )}
      </Card>
    </div>
  );
}
