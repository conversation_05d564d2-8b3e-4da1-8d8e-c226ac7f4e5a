mutation ExecuteCreateProject($input: ProjectInput!) {
    createProject(input: $input) {
      project {
        id
        lid
        alias
        companyName
        aggregator
        implementationType
        startInitialDate
        startFinalDate
        collectionInitialDate
        collectionFinalDate
        migrationInitialDate
        migrationFinalDate
        testInitialDate
        testFinalDate
        month1Test
        month2Test
        goliveInitialDate
        goliveFinalDate
        implementer1 {
          id
          name
          email
        }
        implementer2 {
          id
          name
          email
        }
        backup {
          id
          name
          email
        }
        coordinator {
          id
          name
          email
        }
        team {
          id
          name
          code
        }
        incubator {
          id
          name
          email
        }
        template {
          id
        }
      }
    }
}
