"use client";

import { useState } from "react";

import { ApiReport } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReportsList() {
  const [reports, setReports] = useState<ApiReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchActiveReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getActiveReports();

      setReports(data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch active reports");
    } finally {
      setLoading(false);
    }
  };

  const fetchAllReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getAllReports();

      setReports(data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch all reports");
    } finally {
      setLoading(false);
    }
  };

  const refreshReports = async (includeInactive = false) => {
    if (includeInactive) {
      await fetchAllReports();
    } else {
      await fetchActiveReports();
    }
  };

  return {
    reports,
    loading,
    error,
    fetchActiveReports,
    fetchAllReports,
    refreshReports,
  };
}
