import React from "react";
import { Chip } from "@heroui/react";

interface RolePermissionsProps {
  permissions: string[];
  rolePerms?: { id: number; name: string }[];
}

export function RolePermissions({
  permissions,
  rolePerms,
}: RolePermissionsProps) {
  if (!rolePerms) return null;

  return (
    <div className="flex flex-wrap gap-2">
      {rolePerms.map((perm) => {
        const hasPermission = permissions.includes(perm.name);

        return (
          <Chip
            key={perm.id}
            className="cursor-default"
            color={"default"}
            variant="flat"
          >
            {perm.name}
          </Chip>
        );
      })}
    </div>
  );
}
