import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}
export const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
}: ConfirmModalProps) => {
  return (
    <>
      <Modal
        aria-describedby="confirmation-modal-description"
        aria-labelledby="confirmation-modal-title"
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent>
          <ModalHeader id="confirmation-modal-title">
            Crear proyecto
          </ModalHeader>
          <ModalBody id="confirmation-modal-description">
            ¿Estas seguro que quieres crear el proyecto?
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancelar
            </Button>
            <Button color="primary" onPress={onConfirm}>
              Crear
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};
