export interface ApiProject {
  id: number;
  lid: string;
  alias: string;
  company_name: string;
  aggregator: string;
  implementation_type: string;
  implementer1: number;
  implementer2: number;
  backup: number;
  coordinator: number;
  team: number;
  incubator: number;
  template: number;
  status: string;
  created_at?: string;
}

export interface ApiProjectDates {
  start_initial_date: string | null;
  start_final_date: string | null;
  start_real_initial_date: string | null;
  start_real_final_date: string | null;
  collection_initial_date: string | null;
  collection_final_date: string | null;
  collection_real_initial_date: string | null;
  collection_real_final_date: string | null;
  migration_initial_date: string | null;
  migration_final_date: string | null;
  migration_real_initial_date: string | null;
  migration_real_final_date: string | null;
  test_initial_date: string | null;
  test_final_date: string | null;
  test_real_initial_date: string | null;
  test_real_final_date: string | null;
  month1_test: string | null;
  month2_test: string | null;
  golive_initial_date: string | null;
  golive_final_date: string | null;
  golive_real_initial_date: string | null;
  golive_real_final_date: string | null;
  incubadora_initial_date: string | null;
  incubadora_final_date: string | null;
  incubadora_real_initial_date: string | null;
  incubadora_real_final_date: string | null;
}

export interface ApiSubphase {
  id: number;
  name: string;
  order: number;
  percentage: number;
}

export interface ApiPhase {
  id: number;
  name: string;
  order: number;
  percentage: number;
  verified: boolean;
  completed: boolean;
  subphases: ApiSubphase[];
}

export interface ApiProjectResponse {
  project: ApiProject;
  dates: ApiProjectDates;
  phases: ApiPhase[];
}

export interface ApiField {
  id: number;
  name: string;
  type: "informative" | "selection" | "task" | "document" | "task_with_subtasks";
  value: string | null;
  status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
  is_active: boolean;
  observations: string | null;
  is_milestone: boolean;
  weight: number;
  subphase: string;
  selection_options: any[] | null;
  subtask: any[] | null;
}

export interface ApiPhaseInfo {
  id: number;
  name: string;
  verified: boolean;
  completed: boolean;
  percentage: number;
}

export interface ApiFieldsResponse {
  fields: ApiField[];
  phase: ApiPhaseInfo;
  subphases: ApiSubphase[];
}

export interface UpdateFieldData {
  field_id: number;
  value: string;
  observations: string;
}

export interface UpdateFieldsRequest {
  fields: UpdateFieldData[];
}
