// app/components/ThemeSwitcher.tsx
"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Button } from "@heroui/button";

import { MoonFilledIcon, SunFilledIcon } from "./icons";

export function ThemeSwitch() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div>
      <Button
        isIconOnly
        className="bg-inherit"
        size="sm"
        startContent={
          theme === "dark" ? (
            <SunFilledIcon size={22} />
          ) : (
            <MoonFilledIcon size={22} />
          )
        }
        onPress={() => {
          setTheme(theme === "dark" ? "light" : "dark");
        }}
      />
    </div>
  );
}
