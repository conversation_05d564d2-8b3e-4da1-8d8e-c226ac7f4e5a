# syntax=docker.io/docker/dockerfile:1

FROM oven/bun:1-alpine AS base

# Step 1. Rebuild the source code only when needed
FROM base AS builder

WORKDIR /app

# Install dependencies using bun
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

# Copy app directory structure instead of src
COPY app ./app
COPY components ./components
COPY config ./config
COPY graphql ./graphql
COPY hooks ./hooks
COPY lib ./lib
COPY public ./public
COPY store ./store
COPY styles ./styles
COPY types ./types
COPY utils ./utils
COPY services ./services
COPY next.config.js .
COPY tsconfig.json .
COPY codegen.yml .
COPY tailwind.config.js .
COPY postcss.config.js .

# Environment variables must be present at build time
# https://github.com/vercel/next.js/discussions/14030
ARG NEXT_PUBLIC_GRAPHQL_URI
ENV NEXT_PUBLIC_GRAPHQL_URI=${NEXT_PUBLIC_GRAPHQL_URI}

ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

# Next.js collects completely anonymous telemetry data about general usage. Learn more here: https://nextjs.org/telemetry
# Uncomment the following line to disable telemetry at build time
# ENV NEXT_TELEMETRY_DISABLED 1

# Build Next.js using bun
RUN bun run build

# Note: It is not necessary to add an intermediate step that does a full copy of `node_modules` here

# Step 2. Production image, copy all the files and run next
FROM node:18-alpine AS runner

WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Environment variables must be redefined at run time
ARG NEXT_PUBLIC_GRAPHQL_URI
ENV NEXT_PUBLIC_GRAPHQL_URI=${NEXT_PUBLIC_GRAPHQL_URI}

ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

# Uncomment the following line to disable telemetry at run time
# ENV NEXT_TELEMETRY_DISABLED 1

# Expose the port that the application listens on
EXPOSE 3000

CMD ["node", "server.js"]
