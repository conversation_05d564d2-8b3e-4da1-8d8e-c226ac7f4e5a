# Servidor RMO  
Documentación para gestionar el servidor RMO de Rosclar.  

## Conexión al servidor  

### VPN  
- Conectar con **FortiClient** (credenciales en el password manager de Infini).  

### SSH  
**Requisitos**:  
- **Host/IP**: Consultar en password manager  
- **Puerto**: 22  
- **Usuario/Contraseña**: Consultar en password manager  

**Herramientas recomendadas**:  
- Putty  
- XPipe  
- Termius  

## Gestión de proyectos  

### Estructura actual  
```bash
rmo@rmo:~$ ls
mysql_data_dev  mysql_data_pre  pre  test1  test2-image
```

| Carpeta          | Descripción                                                                 |
|------------------|-----------------------------------------------------------------------------|
| `mysql_data_dev` | Volumen Docker para BD de desarrollo                                        |
| `mysql_data_pre` | Volumen Docker para BD de preproducción                                     |
| `test1`          | Contiene proyectos en preproducción (API + Web)                             |
| Otras            | Carpetas de pruebas sin permisos para borrar por culpa de Docker (｀へ´)✧                                |

### Contenedores Docker  

#### Desarrollo  
| ID       | Imagen      | Puertos                              | Nombre         |
|----------|-------------|--------------------------------------|----------------|
| 873c12d1 | `mysql:latest` | 3307:3306                           | `my-mysql-dev` |

#### Preproducción (`test1`)  
| ID       | Imagen            | Estado       | Puertos               | Nombre            |
|----------|-------------------|--------------|-----------------------|-------------------|
| 8ae94ba5 | `rmo-front-next-app` | Up 2 hours   | 3000:3000             | `rmo-front`       |
| 09d7a13d | `rosclar_rmo-api`    | Unhealthy    | 8000:8000             | `rosclar_rmo-api-1` |
| 22217a8c | `mysql`              | Healthy      | 3308:3306             | `mysql_db`        |

```bash
rmo@rmo:~/test1$ ls
Rosclar_RMO  rmo-front
```

### Gestión de contenedores  

#### API (`Rosclar_RMO`)  
1. **Actualizar código**:  
   ```bash
   git pull
   ```
2. **Reiniciar contenedor**:  
   ```bash
   docker compose down && docker compose up -d --build
   ```
3. **Variables de entorno**:  
   Modificar `docker-compose.yml` (credenciales de BD y configuraciones).  

#### Web (`rmo-front`)  
1. **Actualizar código**:  
   ```bash
   git pull
   ```
2. **Reiniciar contenedor**:  
   ```bash
   docker compose down && docker compose up -d --build
   ```
3. **Variables de entorno**:  
   Modificar `docker-compose.yml` (URLs de API y entorno).  

---  
**Nota**: ¡Recuerda que las credenciales siempre están en el password manager! (•̀ᴗ•́)و ~ O en el docker-compose.yml directamente.(｡•̀ᴗ-)✧