"use client";

import React from "react";

import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

interface ProtectedLayoutProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  fallbackRoute?: string;
}

export function ProtectedLayout({
  children,
  requiredPermission,
  requiredRole,
  fallbackRoute = "/login",
}: ProtectedLayoutProps) {
  return (
    <ProtectedRoute
      fallbackRoute={fallbackRoute}
      requiredPermission={requiredPermission}
      requiredRole={requiredRole}
    >
      {children}
    </ProtectedRoute>
  );
}
