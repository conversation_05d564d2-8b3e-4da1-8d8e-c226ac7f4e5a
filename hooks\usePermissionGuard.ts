"use client";

import { useAuth } from "@/hooks/auth/useAuth";

export interface PermissionGuardProps {
  permission?: string;
  role?: string;
  logicOp?: "AND" | "OR";
}

/**
 * A helper function to check if a user has certain permissions or roles
 * to determine if UI elements like navigation links should be shown
 */
export function usePermissionGuard() {
  const { hasPermission, hasRole } = useAuth();

  const checkAccess = ({
    permission,
    role,
    logicOp = "OR",
  }: PermissionGuardProps): boolean => {
    // If nothing specified, allow access
    if (!permission && !role) return false;

    // If both are specified with AND logic, both must be true
    if (permission && role && logicOp === "AND") {
      return hasPermission(permission) && hasRole(role);
    }

    // If both are specified with OR logic, either can be true
    if (permission && role && logicOp === "OR") {
      return hasPermission(permission) || hasRole(role);
    }

    // If only permission is specified
    if (permission) {
      return hasPermission(permission);
    }

    // If only role is specified
    if (role) {
      return hasRole(role);
    }

    return false;
  };

  return { checkAccess };
}
