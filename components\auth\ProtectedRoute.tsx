"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { useAuth } from "@/hooks/auth/useAuth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  fallbackRoute?: string;
}

export function ProtectedRoute({
  children,
  requiredPermission,
  requiredRole,
  fallbackRoute = "/login",
}: ProtectedRouteProps) {
  const { user, loading, hasPermission, hasRole } = useAuth();
  const router = useRouter();
  const [loadStart, setLoadStart] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    setLoadStart(true);
    if (loading) return;

    // Wait for authentication to complete
    if (!user) {
      router.push(fallbackRoute);

      return;
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      router.push("/unauthorized?code=403");

      return;
    }

    if (requiredRole && !hasRole(requiredRole)) {
      router.push("/unauthorized?code=403");

      return;
    }

    // Only set authorized to true if all checks pass
    setIsAuthorized(true);
    setLoadStart(false);
  }, [
    user,
    loading,
    requiredPermission,
    requiredRole,
    router,
    fallbackRoute,
    hasPermission,
    hasRole,
  ]);

  if (loading || loadStart || !isAuthorized) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-pulse">Cargando...</div>
      </div>
    );
  }

  // Only render children if explicitly authorized
  return <>{children}</>;
}
