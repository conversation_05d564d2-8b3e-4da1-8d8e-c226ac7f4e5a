import { create } from "zustand";
import { persist } from "zustand/middleware";

export type CursorPetType = "none" | "froggo" | "duck";

interface CursorPetStore {
  cursorPet: CursorPetType;
  setCursorPet: (pet: CursorPetType) => void;
}

export const useCursorPetStore = create<CursorPetStore>()(
  persist(
    (set) => ({
      cursorPet: "none",
      setCursorPet: (pet) => set({ cursorPet: pet }),
    }),
    {
      name: "cursor-pet-storage",
    },
  ),
);
