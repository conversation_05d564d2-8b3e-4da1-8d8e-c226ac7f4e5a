"use client";

import { Key, useState, useEffect } from "react";
import { Tab, Tabs } from "@heroui/react";
import { Button } from "@heroui/button";
import { RiFileExcel2Fill } from "react-icons/ri";
import NextLink from "next/link";

import { title } from "@/components/primitives";
import ProjectsTable from "@/components/projects/projects-table";
import ProjectsGantt from "@/components/projects/projects-gantt";
import { useAuth } from "@/hooks/auth/useAuth";

export default function Home() {
  const { hasPermission } = useAuth();
  const [canEditProjects, setCanEditProjects] = useState(
    false
  );

  useEffect(() => {
    setCanEditProjects(hasPermission("editar_proyectos"));
  }, [hasPermission]);

  const [selectedTab, setSelectedTab] = useState("table");

  const handleTabChange = (key: Key) => {
    setSelectedTab(key.toString());
  };

  return (
    <section className="flex flex-col">
      <div className="flex justify-between items-center w-full h-auto">
        <h2 className={title({ size: "sm" })}>Seguimiento de Proyectos</h2>
        <div className="flex items-center">
          <Tabs
            key="solid"
            aria-label="Tabs variants"
            selectedKey={selectedTab}
            variant="solid"
            onSelectionChange={handleTabChange}
          >
            <Tab key="table" title="Tabla" />
            <Tab key="schedule" title="Cronograma" />
          </Tabs>
          <NextLink href="/proyecto/crear">
            <Button className={"ml-2"} color="primary" href="/proyecto/crear" isDisabled={!canEditProjects}>
              Crear proyecto
            </Button>
          </NextLink>
          <Button
            isIconOnly
            aria-label="Exportar a Excel"
            className={"ml-2"}
            color="primary"
            isDisabled={selectedTab === "schedule"}
          >
            <RiFileExcel2Fill size={30} />
          </Button>
        </div>
      </div>

      <div className={"w-full"}>
        {selectedTab === "table" ? <ProjectsTable /> : <ProjectsGantt />}
      </div>
    </section>
  );
}
