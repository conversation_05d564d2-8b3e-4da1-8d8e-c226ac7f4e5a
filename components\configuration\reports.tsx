"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dalFooter,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { TbBulb, TbBulbOff } from "react-icons/tb";

import { ReportViewModal } from "./report-view-modal";
import { ReportEditModal } from "./report-edit-modal";

import { ApiReport } from "@/types/report";
import { useReports } from "@/hooks/reports/useReports";
import { MdOutlineToggleOn, MdToggleOff } from "react-icons/md";

interface ReportsProps {
  isCreating: boolean;
  setIsCreating: (value: boolean) => void;
  canEditConfiguration: boolean;
}

export default function Reports({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: ReportsProps) {
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<ApiReport | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<ApiReport | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [reportToEdit, setReportToEdit] = useState<ApiReport | null>(null);
  const router = useRouter();
  const {
    reports: apiReports,
    loading,
    error,
    fetchReports,
    deleteReport,
    toggleReportStatus,
  } = useReports();

  useEffect(() => {
    fetchReports();
  }, []);

  useEffect(() => {
    if (!isCreating) return;
    setIsCreating(false);
    router.push("/configuracion/reportes/crear");
  }, [isCreating]);

  const handleViewReport = (report: ApiReport) => {
    setSelectedReport(report);
    setIsViewModalOpen(true);
  };

  const handleEditReport = (report: ApiReport) => {
    setReportToEdit(report);
    setIsEditModalOpen(true);
  };

  const handleCloneReport = (report: ApiReport) => {
    // Navigate to create report page with pre-filled data
    const params = new URLSearchParams({
      name: `${report.name} (Copia)`,
      description: report.description,
      document_name: `${report.document_name}_copy`,
      id: report.id.toString(),
    });

    router.push(`/configuracion/reportes/crear?${params.toString()}`);
  };

  const handleDeleteReport = (report: ApiReport) => {
    setReportToDelete(report);
    setIsDeleteModalOpen(true);
  };

  const handleToggleReport = async (report: ApiReport) => {
    const result = await toggleReportStatus(report.id);

    if (result.success) {
      // Report status updated successfully
      // The state is already updated in the hook
    }
  };

  const handleViewModalClose = () => {
    setIsViewModalOpen(false);
    setSelectedReport(null);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setReportToEdit(null);
  };

  const handleSaveReport = () => {
    // Refresh reports after save
    fetchReports();
  };

  const confirmDeleteReport = async () => {
    if (!reportToDelete) return;

    const result = await deleteReport(reportToDelete.id);

    if (result.success) {
      setIsDeleteModalOpen(false);
      setReportToDelete(null);
    }
  };

  const handleDeleteModalClose = () => {
    setIsDeleteModalOpen(false);
    setReportToDelete(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-danger">{error}</p>
        <Button
          className="mt-4"
          color="primary"
          variant="flat"
          onPress={fetchReports}
        >
          Reintentar
        </Button>
      </div>
    );
  }

  return (
    <div className="pt-4 w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {apiReports
          .sort((a, b) => a.name.localeCompare(b.name))
          .sort((a, b) => (a.active === b.active ? 0 : a.active ? -1 : 1))
          .map((report) => (
            <Card
              key={report.id}
              className="border border-default-200 cursor-pointer h-[185px] flex flex-col"
            >
              <CardBody className="p-0 flex-grow overflow-hidden">
                <div className="p-4 h-full flex flex-col">
                  <Tooltip content={report.name}>
                    <h3 className="text-large font-medium mb-2 line-clamp-1 overflow-hidden text-ellipsis">
                      {report.name}
                    </h3>
                  </Tooltip>
                  <Tooltip content={report.description}>
                    <p className="text-small text-default-500 line-clamp-3 overflow-hidden">
                      {report.description}
                    </p>
                  </Tooltip>
                </div>
              </CardBody>
              <CardFooter className="flex justify-between items-center px-4 py-3 border-t border-default-200 mt-auto">
                <Button
                  color="primary"
                  isDisabled={!canEditConfiguration}
                  size="sm"
                  startContent={<Icon className="text-lg" icon="lucide:copy" />}
                  variant="flat"
                  onPress={() => handleCloneReport(report)}
                >
                  Clonar reporte
                </Button>
                <div>
                  <Button
                    isIconOnly
                    aria-label="Edit report"
                    color="primary"
                    isDisabled={!canEditConfiguration}
                    size="sm"
                    variant="light"
                    onPress={() => handleEditReport(report)}
                  >
                    <Icon className="text-lg" icon="lucide:edit-3" />
                  </Button>
                  <Button
                    isIconOnly
                    aria-label={
                      report.active ? "Disable report" : "Enable report"
                    }
                    color={!report.active ? "danger" : "success"}
                    size="sm"
                    variant="light"
                    onPress={() => handleToggleReport(report)}
                  >
                    {/* <Icon
                      className="text-lg"
                      icon={
                        report.active
                          ? "lucide:toggle-right"
                          : "lucide:toggle-left"
                      }
                    /> */}

                    {!report.active ? (
                      <MdOutlineToggleOn size={20} />
                    ) : (
                      <MdToggleOff size={20} />
                    )}
                  </Button>
                  {/* <Button
                    isIconOnly
                    aria-label="Delete report"
                    color="danger"
                    isDisabled={!canEditConfiguration}
                    size="sm"
                    variant="light"
                    onPress={() => handleDeleteReport(report)}
                  >
                    <Icon className="text-lg" icon="lucide:trash-2" />
                  </Button> */}
                  <Button
                    isIconOnly
                    aria-label="Preview report"
                    size="sm"
                    variant="light"
                    onPress={() => handleViewReport(report)}
                  >
                    <Icon className="text-lg" icon="lucide:eye" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
      </div>

      <ReportViewModal
        isOpen={isViewModalOpen}
        report={selectedReport}
        onClose={handleViewModalClose}
      />

      <ReportEditModal
        isOpen={isEditModalOpen}
        report={reportToEdit}
        onClose={handleEditModalClose}
        onSave={handleSaveReport}
      />

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteModalOpen} onClose={handleDeleteModalClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <h3 className="text-lg font-medium">Eliminar Reporte</h3>
              </ModalHeader>
              <ModalBody>
                <p>
                  ¿Estás seguro de que deseas eliminar el reporte{" "}
                  <strong>{reportToDelete?.name}</strong>?
                </p>
                <p className="text-small text-default-500">
                  Esta acción no se puede deshacer.
                </p>
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" onPress={onClose}>
                  Cancelar
                </Button>
                <Button color="danger" onPress={confirmDeleteReport}>
                  Eliminar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
}
