"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface Contact {
  id: number;
  name: string;
  email: string;
  type: string;
  position: string;
  project: number;
  created_at: string;
  project_lid?: string;
  project_company_name?: string;
}

export function useContacts() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllContacts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(API_ROUTES.ALL_CONTACTS);

      if (response.status === 200) {
        setContacts(response.data);

        return response.data;
      } else {
        setError("Failed to fetch contacts");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch contacts");
    } finally {
      setLoading(false);
    }
  };

  const fetchContactsByProject = async (projectId: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(
        `${API_ROUTES.GET_CONTACTS_BY_PROJECT}?project_id=${projectId}`,
      );

      if (response.status === 200) {
        setContacts(response.data);

        return response.data;
      } else {
        setError("Failed to fetch contacts");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch contacts");
    } finally {
      setLoading(false);
    }
  };

  const createContact = async (
    contactData: Omit<Contact, "id" | "created_at">,
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.post(
        API_ROUTES.CREATE_CONTACT,
        contactData,
      );

      if (response.status === 201) {
        // Update contacts state with the new contact
        setContacts((prevContacts) => [...prevContacts, response.data]);

        return response.data;
      } else {
        setError("Failed to create contact");
      }
    } catch (err: any) {
      setError(err.message || "Failed to create contact");
    } finally {
      setLoading(false);
    }
  };

  const getContact = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(
        API_ROUTES.GET_CONTACT.replace("{id}", id.toString()),
      );

      if (response.status === 200) {
        return response.data;
      } else {
        setError("Failed to fetch contact");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch contact");
    } finally {
      setLoading(false);
    }
  };

  const updateContact = async (
    id: number,
    contactData: Omit<Contact, "id" | "created_at">,
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.put(
        API_ROUTES.UPDATE_CONTACT.replace("{id}", id.toString()),
        contactData,
      );

      if (response.status === 200) {
        // Update contacts state with the updated contact
        setContacts((prevContacts) =>
          prevContacts.map((contact) =>
            contact.id === id ? response.data : contact,
          ),
        );

        return response.data;
      } else {
        setError("Failed to update contact");
      }
    } catch (err: any) {
      setError(err.message || "Failed to update contact");
    } finally {
      setLoading(false);
    }
  };

  const deleteContact = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.delete(
        API_ROUTES.DELETE_CONTACT.replace("{id}", id.toString()),
      );

      if (response.status === 204) {
        // Remove the contact from the state
        setContacts((prevContacts) =>
          prevContacts.filter((contact) => contact.id !== id),
        );

        return true;
      } else {
        setError("Failed to delete contact");
      }
    } catch (err: any) {
      setError(err.message || "Failed to delete contact");
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,

    fetchAllContacts,
    createContact,
    getContact,
    updateContact,
    deleteContact,
    fetchContactsByProject,

    contacts,
  };
}
