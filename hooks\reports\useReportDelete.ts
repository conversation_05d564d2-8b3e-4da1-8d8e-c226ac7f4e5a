"use client";

import { useState } from "react";

import { ReportsService } from "@/services/reports";

export function useReportDelete() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteReport = async (
    id: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      await ReportsService.deleteReport(id);

      return { success: true };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to delete report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    deleteReport,
  };
}
