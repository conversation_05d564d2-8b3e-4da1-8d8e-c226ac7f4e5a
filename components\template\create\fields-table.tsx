import React, { useMemo, useState } from "react";
import {
  Input,
  Button,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Pagination,
} from "@heroui/react";

import { Field } from "@/types/fields";
import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";

export default function FieldsTable() {
  const [fields, setFields] = useState<Field[]>([
    {
      id: 1,
      name: "Nombre del campo",
      description: "Descripción del campo",
      field_type: "INFORMATIVE",
      phase: "Start",
      subphase: "Subfase 1",
      hito: "Hito 1",
      weight: 10,
    },
    {
      id: 2,
      name: "Campo de selección",
      description: "Descripción del campo de selección",
      field_type: "SELECTION",
      phase: "Collection",
      subphase: "Subfase 2",
      hito: "Hito 2",
      weight: 20,
    },
  ]);

  const [selectedKeys, setSelectedKeys] = useState<Set<number>>(new Set([]));
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFields, setFilteredFields] = useState<Field[]>(fields);
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);
  const rowsPerPage = 10;

  // Compute selected fields whenever selectedKeys changes
  const selectedFields = React.useMemo(() => {
    return fields.filter((field) => selectedKeys.has(field.id));
  }, [selectedKeys, fields]);

  const handleSelectionChange = (keys: Set<React.Key>) => {
    console.log("Selected keys:", keys);
    // TODO: Select only the keys on the filters shown
    if (keys.has("NaN") || keys.has(NaN)) {
      // Select all fields
      setSelectedKeys(new Set(fields.map(field => field.id)));
    } else {
      // Convert string keys to numbers
      setSelectedKeys(new Set(Array.from(keys).map((key) => Number(key))));
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    applyFiltersAndSort(value, activeFilters, sortConfig);
  };

  const getUniqueValues = (column: keyof Field) => {
    return Array.from(new Set(fields.map((field) => String(field[column]))));
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    const newSortConfig = { column, direction };

    setSortConfig(newSortConfig);
    applyFiltersAndSort(searchTerm, activeFilters, newSortConfig);
  };

  const handleFielterChange = (column: string, selectedValues: string[]) => {
    const newFilters = { ...activeFilters, [column]: selectedValues };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }
    setActiveFilters(newFilters);
    applyFiltersAndSort(searchTerm, newFilters, sortConfig);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
    setSearchTerm("");
    setFilteredFields(fields);
    setPage(1);
  };

  const applyFiltersAndSort = (
    term: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
  ) => {
    let filtered = fields;

    if (term) {
      filtered = filtered.filter(
        (field) =>
          field.name.toLowerCase().includes(term.toLowerCase()) ||
          field.description.toLowerCase().includes(term.toLowerCase()) ||
          field.field_type.toLowerCase().includes(term.toLowerCase()) ||
          field.phase.toLowerCase().includes(term.toLowerCase()),
      );
    }

    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((field) =>
          values.includes(String(field[column as keyof Field])),
        );
      }
    });

    if (sort) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = String(a[sort.column as keyof Field]);
        const bValue = String(b[sort.column as keyof Field]);

        if (sort.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    setFilteredFields(filtered);
    setPage(1);
  };

  const pages = Math.ceil(filteredFields.length / rowsPerPage);

  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredFields.slice(start, end);
  }, [page, filteredFields]);

  return (
    <div className="w-full pt-4">
      <div className="w-full mb-4 flex flex-col sm:flex-row gap-4 justify-between items-center">
        <div className="flex gap-2 w-full sm:w-auto">
          <Input
            className="w-full sm:w-96"
            placeholder="Buscar por nombre, descripción o tipo"
            startContent={"🔍"}
            value={searchTerm}
            onValueChange={handleSearch}
          />
        </div>
        <div className="flex gap-2 w-full sm:w-auto justify-end">
          <Button
            color={
              Object.keys(activeFilters).length > 0 || searchTerm
                ? "primary"
                : "default"
            }
            variant="flat"
            onPress={handleClearFilters}
          >
            Borrar filtros{" "}
            {(Object.keys(activeFilters).length > 0 || searchTerm) &&
              `(${Object.keys(activeFilters).length + (searchTerm ? 1 : 0)})`}
          </Button>
        </div>
      </div>

      {/* Display selected count */}
      {selectedFields.length > 0 && (
        <div className="mb-4 p-2 bg-primary-50 dark:bg-primary-900 rounded-lg">
          <p className="text-primary-600 dark:text-primary-300">
            {selectedFields.length} campo(s) seleccionado(s)
          </p>
        </div>
      )}

      <Table
        removeWrapper
        aria-label="Tabla de campos"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              isCompact
              showControls
              showShadow
              color="primary"
              page={page}
              total={pages}
              onChange={setPage}
            />
          </div>
        }
        selectionMode="multiple"
        onSelectionChange={(keys) =>
          handleSelectionChange(
            new Set(
              Array.from(keys as Set<React.Key>).map((key) => Number(key)),
            ),
          )
        }
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre del campo"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="description"
              items={getUniqueValues("description")}
              sortConfig={sortConfig}
              title="Descripción del campo"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="field_type"
              items={getUniqueValues("field_type")}
              sortConfig={sortConfig}
              title="Tipo de campo"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="phase"
              items={getUniqueValues("phase")}
              sortConfig={sortConfig}
              title="Fase del campo"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="subphase"
              items={getUniqueValues("subphase")}
              sortConfig={sortConfig}
              title="Subfase del campo"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="hito"
              items={getUniqueValues("hito")}
              sortConfig={sortConfig}
              title="Hito"
              onFilter={handleFielterChange}
              onSort={handleSort}
            />
          </TableColumn>
        </TableHeader>
        <TableBody emptyContent={"No hay campos disponibles"} items={items}>
          {(item) => (
            <TableRow key={item.id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.description}</TableCell>
              <TableCell>{item.field_type}</TableCell>
              <TableCell>{item.phase}</TableCell>
              <TableCell>{item.subphase}</TableCell>
              <TableCell>{item.hito}</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
