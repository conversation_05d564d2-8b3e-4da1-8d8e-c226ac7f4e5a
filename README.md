# RMO Front

## Descripción
Frontend para el sistema de gestión de proyectos RMO, desarrollado con Next.js y utilizando Bun como gestor de paquetes y entorno de ejecución.

## Requisitos previos
- [Bun](https://bun.sh/) instalado en tu sistema
- Node.js (recomendado, aunque Bun puede funcionar sin él)
- Git

## Instalación

### 1. Clonar el repositorio
```bash
git clone <url-del-repositorio>
cd rmo-front
```

### 2. Instalar dependencias
```bash
bun install
```

### 3. Configurar variables de entorno
Crea un archivo `.env` en la raíz del proyecto con las siguientes variables:

```env
# API y GraphQL
NEXT_PUBLIC_GRAPHQL_URI=http://localhost:8000/graphql/

# Otras configuraciones
# Agrega cualquier otra variable de entorno necesaria
```

## Ejecución del proyecto

### Modo desarrollo
```bash
bun run dev
```
Esto iniciará el servidor de desarrollo en modo turbopack en el puerto 3003. Puedes acceder a la aplicación en [http://localhost:3003](http://localhost:3003).

### Compilar para producción
```bash
bun run build
```

### Iniciar en modo producción
```bash
bun run start
```

## Comandos útiles

### Lint
```bash
bun run lint
```

### Generar código GraphQL
```bash
bun run codegen
```

## Docker

También puedes ejecutar el proyecto utilizando Docker:

### Construir y ejecutar con Docker Compose
```bash
docker-compose up -d
```

### Sólo construir la imagen
```bash
docker build -t rmo-front .
```

### Ejecutar el contenedor
```bash
docker run -p 3000:3000 rmo-front
```

## Estructura del proyecto

- `/app`: Rutas y páginas de la aplicación (Next.js App Router)
- `/components`: Componentes reutilizables
- `/config`: Configuraciones generales
- `/graphql`: Operaciones y esquemas GraphQL
- `/hooks`: Hooks personalizados
- `/lib`: Utilidades y configuraciones de bibliotecas
- `/public`: Archivos estáticos
- `/store`: Estado global (Zustand)
- `/styles`: Estilos globales
- `/types`: Definiciones de tipos TypeScript
- `/utils`: Funciones de utilidad

## Tecnologías principales
- Next.js 15
- React 18
- GraphQL con Apollo Client
- Tailwind CSS
- Bun como runtime y gestor de paquetes
- HeroUI para componentes de interfaz

## Contacto
Para cualquier problema o sugerencia, por favor contacta al equipo de desarrollo.
 
