"use client";

import { useState } from "react";

import { User } from "./useUsers";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useUsersList() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_USERS);

      if (response.status === 200) {
        setUsers(response.data);
      } else {
        setError("Failed to fetch users");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const syncUsersWithCore = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.post(API_ROUTES.SYNC_USERS_CORE);

      if (response.status === 200) {
        // Refresh the users list
        await fetchUsers();

        return { success: true, data: response.data };
      } else {
        setError("Failed to sync users with core");

        return { success: false, error: "Failed to sync users with core" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to sync users with core";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    users,
    loading,
    error,
    fetchUsers,
    syncUsersWithCore,
  };
}
