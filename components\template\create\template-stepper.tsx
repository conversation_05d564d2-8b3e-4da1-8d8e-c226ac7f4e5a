import React, { useState } from "react";
import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

interface Step {
  key: number;
  title: string;
  description: string;
}

interface TemplateData {
  name: string;
  description: string;
  type: string;
}

interface TemplateStepperProps {
  steps: Step[];
  currentStep: number;
  onStepClick: (step: number) => void;
  templateData?: TemplateData;
}

export default function TemplateStepper({
  steps,
  currentStep,
  onStepClick,
  templateData,
}: TemplateStepperProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="w-full mb-4 relative overflow-hidden">
      {/* Toggle Button */}
      <Button
        isIconOnly
        size="sm"
        variant="light"
        className="absolute bottom-2 right-2 z-10 bg-transparent hover:bg-default-100 transition-all duration-200"
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <Icon
          icon="heroicons:chevron-down"
          width={14}
          className={`transition-transform duration-300 ease-in-out ${
            isExpanded ? "rotate-180" : "rotate-0"
          }`}
        />
      </Button>

      {/* Compact Stepper View */}
      <div className="flex items-center justify-between py-4">
        {steps.map((step, index) => {
          const isActive = step.key === currentStep;
          const isCompleted = step.key < currentStep;
          const isClickable = step.key <= currentStep;

          return (
            <React.Fragment key={step.key}>
              {/* Step Circle and Info */}
              <div className="flex flex-col items-center">
                <Button
                  isIconOnly
                  className={`w-10 h-10 rounded-full border-2 transition-all duration-200 mb-2 ${
                    isActive
                      ? "bg-primary border-primary text-primary-foreground shadow-md"
                      : isCompleted
                        ? "bg-success border-success text-success-foreground shadow-sm"
                        : "bg-default-100 border-default-300 text-default-500"
                  } ${isClickable ? "cursor-pointer hover:scale-105" : "cursor-default"}`}
                  disabled={!isClickable}
                  onPress={() => isClickable && onStepClick(step.key)}
                >
                  {isCompleted ? (
                    <Icon icon="heroicons:check" width={16} />
                  ) : (
                    <span className="text-sm font-bold">{step.key}</span>
                  )}
                </Button>

                {/* Step Title */}
                <p
                  className={`text-xs font-medium text-center max-w-[100px] leading-tight ${
                    isActive
                      ? "text-primary"
                      : isCompleted
                        ? "text-success"
                        : "text-default-500"
                  }`}
                >
                  {step.title}
                </p>

                {/* Step Description - Animated */}
                <div
                  className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    isExpanded ? "max-h-20 opacity-100 mt-1" : "max-h-0 opacity-0 mt-0"
                  }`}
                >
                  <p className="text-xs text-default-400 text-center max-w-[120px] leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-3">
                  <div
                    className={`h-0.5 rounded-full transition-all duration-300 ${
                      step.key < currentStep
                        ? "bg-gradient-to-r from-success to-success-400"
                        : "bg-default-300"
                    }`}
                  />
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Template Data - Animated */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded && templateData
            ? "max-h-32 opacity-100"
            : "max-h-0 opacity-0"
        }`}
      >
        {templateData && (
          <div className="mt-4 pt-4 border-t border-default-200">
            <div className="flex justify-between space-x-6">
              <div className="flex-1 text-center">
                <h3 className="text-xs font-semibold text-default-500">Nombre</h3>
                <p className="text-sm">{templateData.name}</p>
              </div>
              <div className="flex-1 text-center">
                <h3 className="text-xs font-semibold text-default-500">Descripción</h3>
                <p className="text-sm">{templateData.description}</p>
              </div>
              <div className="flex-1 text-center">
                <h3 className="text-xs font-semibold text-default-500">Plantilla base</h3>
                <p className="text-sm capitalize">{templateData.type}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
