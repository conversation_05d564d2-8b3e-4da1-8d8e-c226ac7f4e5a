"use client";

import { useEffect, useState } from "react";

import { ProjectUser } from "./useUsers";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useProjectUsers() {
  const [projectUsers, setProjectUsers] = useState<ProjectUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjectUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.PROJECT_USERS);

      if (response.status === 200) {
        setProjectUsers(response.data);
      } else {
        setError("Failed to fetch project users");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch project users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjectUsers();
  }, []);

  return {
    projectUsers,
    loading,
    error,
    fetchProjectUsers,
  };
}
