import type { Role, Permission } from "@/types/role";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Checkbox,
  Spinner,
} from "@heroui/react";

import { usePermissions } from "@/hooks/users/usePermissions";
import { useRoles } from "@/hooks/users/useRoles";

interface RoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: Role | null;
  onConfirm: (role?: Role) => void;
  mode: "edit" | "delete" | "create";
}

export function RoleModal({
  isOpen,
  onClose,
  role,
  onConfirm,
  mode,
}: RoleModalProps) {
  const {
    permissions,
    loading: loadingPermissions,
    fetchPermissions,
  } = usePermissions();
  const { createRole, updateRole, deleteRole } = useRoles();
  const [name, setName] = useState(role?.name || "");
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPermissions();
  }, []);

  useEffect(() => {
    if (role) {
      setName(role.name);
      // Handle different types of permission arrays
      if (role.permissions && role.permissions.length > 0) {
        if (typeof role.permissions[0] === "number") {
          setSelectedPermissions(role.permissions as number[]);
        } else if (
          typeof role.permissions[0] === "object" &&
          "id" in role.permissions[0]
        ) {
          setSelectedPermissions(
            (role.permissions as Permission[]).map((p) => p.id),
          );
        } else if (typeof role.permissions[0] === "string") {
          // Convert permission names to IDs if needed
          const permissionIds = (role.permissions as string[])
            .map((permName) => {
              const foundPerm = permissions.find((p) => p.name === permName);

              return foundPerm ? foundPerm.id : null;
            })
            .filter((id): id is number => id !== null);

          setSelectedPermissions(permissionIds);
        }
      } else {
        setSelectedPermissions([]);
      }
    } else {
      setName("");
      setSelectedPermissions([]);
    }
  }, [role, permissions]);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      if (mode === "delete" && role) {
        const result = await deleteRole(role.id);

        if (result.success) {
          onConfirm();
        } else {
          setError(result.error || "Failed to delete role");
        }
      } else if (mode === "edit" && role) {
        const result = await updateRole(role.id, selectedPermissions);

        if (result.success) {
          onConfirm();
        } else {
          setError(result.error || "Failed to update role");
        }
      } else if (mode === "create") {
        const result = await createRole(name, selectedPermissions);

        if (result.success) {
          onConfirm();
        } else {
          setError(result.error || "Failed to create role");
        }
      }

      if (!error) {
        onClose();
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalContent>
        <ModalHeader>
          {mode === "edit"
            ? "Editar rol"
            : mode === "delete"
              ? "Eliminar rol"
              : "Crear rol"}
        </ModalHeader>
        <ModalBody>
          {error && <div className="text-danger mb-4">{error}</div>}

          {mode === "delete" ? (
            <p>
              ¿Estas seguro que quieres eliminar el rol &quot;{role?.name}
              &quot;?
            </p>
          ) : (
            <div className="flex flex-col gap-4">
              <Input
                isDisabled={mode === "edit"} // Name is not editable in edit mode
                label="Nombre del rol"
                value={name}
                onValueChange={setName}
              />
              <div className="flex flex-col gap-2">
                <p className="text-sm font-medium">Permisos</p>
                {loadingPermissions ? (
                  <Spinner label="Cargando permisos..." />
                ) : (
                  <div className="grid grid-cols-2 gap-2">
                    {permissions.map((permission) => (
                      <Checkbox
                        key={permission.id}
                        isSelected={selectedPermissions.includes(permission.id)}
                        onValueChange={(isSelected) => {
                          setSelectedPermissions(
                            isSelected
                              ? [...selectedPermissions, permission.id]
                              : selectedPermissions.filter(
                                  (p) => p !== permission.id,
                                ),
                          );
                        }}
                      >
                        {permission.name}
                      </Checkbox>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button isDisabled={isSubmitting} variant="flat" onPress={onClose}>
            Cancelar
          </Button>
          <Button
            color={mode === "delete" ? "danger" : "primary"}
            isLoading={isSubmitting}
            onPress={handleConfirm}
          >
            {mode === "delete" ? "Eliminar" : "Guardar"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
