export function getGradientFromName(name: string, darkMode = false) {
  const hash = name
    .split("")
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);

  const gradients = [
    ["#FF9A8B", "#FF6B95"], // <PERSON> → Pink
    ["#FFD3A5", "#FD6585"], // Peach → Hot Pink
    ["#A0E7E5", "#B5FFFC"], // Mint → Ice
    ["#84FAB0", "#8FD3F4"], // Aqua → Sky
    ["#D9AFD9", "#97D9E1"], // Lavender → Seafoam
    ["#FBC2EB", "#A6C1EE"], // Blush → Periwinkle
    ["#F6D365", "#FDA085"], // <PERSON> → Sunset
    ["#FFC3A0", "#FFAFBD"], // Apricot → Rose
    ["#A8FF78", "#78FFD6"], // Lime → Turquoise
    ["#FDCBF1", "#E6DEE9"], // Cotton Candy → Pearl
    ["#6A11CB", "#2575FC"], // Royal Purple → Electric Blue
    ["#F36265", "#961276"], // Cherry → Wine
    ["#43CBFF", "#9708CC"], // Cyan → Magenta
    ["#FFE29F", "#FFA99F"], // Buttercup → Salmon
    ["#E0C3FC", "#8EC5FC"], // Lilac → Cornflower
    ["#FF9D6C", "#BB4E75"], // Tangerine → Berry
  ];

  const darkModeGradients = [
    ["#4A148C", "#880E4F"], // Deep Purple → Dark Pink
    ["#3E2723", "#BF360C"], // Dark Brown → Deep Orange
    ["#004D40", "#00695C"], // Teal → Dark Teal
    ["#1B5E20", "#2E7D32"], // Forest Green → Green
    ["#263238", "#37474F"], // Blue Grey → Dark Grey
    ["#311B92", "#512DA8"], // Indigo → Deep Indigo
    ["#0D47A1", "#1565C0"], // Dark Blue → Blue
    ["#1A237E", "#283593"], // Navy → Indigo
    ["#4E342E", "#6D4C41"], // Coffee → Brown
    ["#212121", "#424242"], // Charcoal → Grey
    ["#880E4F", "#AD1457"], // Dark Pink → Crimson
    ["#B71C1C", "#C62828"], // Dark Red → Red
    ["#1B5E20", "#388E3C"], // Dark Green → Green
    ["#F57F17", "#F9A825"], // Mustard → Yellow
    ["#3E2723", "#5D4037"], // Dark Chocolate → Cocoa
    ["#4A148C", "#6A1B9A"], // Deep Purple → Purple
  ];

  const index = hash % gradients.length;

  const selectedGradients = darkMode ? darkModeGradients : gradients;

  return {
    from: selectedGradients[index][0],
    to: selectedGradients[index][1],
  };
}

export function getFieldTypeDisplayName(type: string): string {
  type = type.toLowerCase();

  switch (type) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "Subtarea";
    default:
      return type;
  }
}
