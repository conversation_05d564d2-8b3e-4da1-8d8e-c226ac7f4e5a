// components/auth/RequirePermission.tsx
"use client";

import React from "react";
import { useRouter } from "next/navigation";

import { useUserStore } from "@/store/use-user-store";
import { useRoles } from "@/hooks/users/useRoles";

interface RequirePermissionProps {
  /** The specific permission needed to view the content */
  permission?: string;
  /** Role name required to view the content */
  role?: string;
  /** Whether to hide content completely when unauthorized (true) or show fallback (false) */
  hideContent?: boolean;
  /** Content to show when user has permission */
  children: React.ReactNode;
  /** Optional fallback UI when permission is denied - only shown if hideContent is false */
  fallback?: React.ReactNode;
}

export function RequirePermission({
  permission,
  role,
  hideContent = false,
  children,
  fallback = <p>No tienes permisos para ver este contenido.</p>,
}: RequirePermissionProps) {
  const { userInfo } = useUserStore();
  const { roles } = useRoles();
  const router = useRouter();

  const hasPermission = React.useMemo(() => {
    if (!userInfo) return false;

    // Find user's role from their groups
    const userRole =
      userInfo.groups && userInfo.groups.length > 0
        ? userInfo.groups[0] // Using first group as role
        : null;

    if (!userRole) {
      console.log("No user role found, returning false");

      return false;
    }

    // Find role object from roles array from useUsers hook
    const userRoleObj =
      userRole && roles
        ? roles.find(
            (r) => r.name.toLowerCase() === userRole.name.toLowerCase(),
          )
        : null;

    if (!userRoleObj) return false;

    // Check if specific permission is required
    if (permission) {
      // @ts-ignore - Permission type mismatch
      return userRoleObj.permissions?.includes(permission) || false;
    }

    // Check if specific role is required
    if (role) {
      return userRoleObj.name.toLowerCase() === role.toLowerCase();
    }

    // If neither permission nor role specified, allow access
    return true;
  }, [userInfo, permission, role, roles]);

  // If user doesn't have permission and we should hide content completely
  if (!hasPermission && hideContent) {
    return null;
  }

  // If user has permission, show the children
  if (hasPermission) {
    return <>{children}</>;
  }

  // Otherwise show the fallback
  return <>{fallback}</>;
}

// Higher-order component to protect entire pages
export function withPermission(
  Component: React.ComponentType,
  requiredPermission?: string,
  requiredRole?: string,
) {
  return function ProtectedComponent(props: any) {
    const { userInfo } = useUserStore();
    const { roles } = useRoles();
    const router = useRouter();

    React.useEffect(() => {
      if (!userInfo) {
        router.push("/login");

        return;
      }

      const userRole =
        userInfo.groups && userInfo.groups.length > 0
          ? userInfo.groups[0]
          : null;

      const userRoleObj =
        userRole && roles
          ? roles.find(
              (r) => r.name.toLowerCase() === userRole.name.toLowerCase(),
            )
          : null;

      let hasAccess = false;

      if (userRoleObj) {
        if (requiredPermission) {
          // @ts-ignore - Permission type mismatch
          hasAccess = userRoleObj.permissions?.includes(requiredPermission) || false;
        } else if (requiredRole) {
          hasAccess =
            userRoleObj.name.toLowerCase() === requiredRole.toLowerCase();
        } else {
          hasAccess = true;
        }
      }

      if (!hasAccess) {
        router.push("/error?code=403");
      }
    }, [userInfo, router, roles]);

    return <Component {...props} />;
  };
}
