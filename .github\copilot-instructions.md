# RMO Frontend Copilot Instructions

## Project Overview
This is a Next.js 15 project management frontend using App Router, TypeScript, and Bun as the runtime/package manager. The app manages project implementation lifecycle with phases, percentages, status tracking, and role-based permissions.

## Tech Stack & Key Dependencies
- **Runtime**: Bun (use `bun run dev --turbopack --port 3003`)
- **UI**: HeroUI (comprehensive component library) + Tailwind CSS
- **State**: Zustand with persistence for user/app state
- **Data**: Apollo Client for GraphQL (backend at `localhost:8000/graphql/`)
- **Auth**: JWT tokens stored in localStorage with permission-based access control

## Critical Patterns

### GraphQL Code Generation
Run `bun run codegen` after GraphQL schema changes. Operations are in `./graphql/operations/**/*.graphql` and generate TypeScript types in `./graphql/schemas/generated.ts`.

### Status/Phase Mapping Pattern
API returns English enums, UI displays Spanish. Use existing mappers:
```typescript
// Status: "IN_PROGRESS" → "En curso", "SCHEDULED" → "Prevista", etc.
mapStatusToSpanish(status: string): string

// Phase styling functions in components/primitives.ts:
getPhaseStyle(phase: string, isDarkMode: boolean)
getPhaseStyleText(phase: string, isDarkMode: boolean)
getStatusStyleText(status: string, isDarkMode: boolean)
```

### Project Data Transformation
Projects from GraphQL require complex transformation in `components/projects/projects-table.tsx`:
- Parse `percentages` array (JSON strings) into phase-specific percentages (S, C, M, T, G, I)
- Transform `goliveFinalDate` from YYYY-MM-DD to YYYY-MM format
- Map implementer object to display name string
- Calculate total percentage as average of all phases

### Permission-Based UI
Use `RequirePermission` component and `useAuth` hook:
```tsx
<RequirePermission permission="editar_proyectos">
  <Button isDisabled={!canModifyProjects} />
</RequirePermission>

// In components:
const { hasPermission } = useAuth();
const [canModifyProjects, setCanModifyProjects] = useState(false);
useEffect(() => {
  setCanModifyProjects(hasPermission("editar_proyectos"));
}, [hasPermission]);
```

### HeroUI Component Usage
- Use HeroUI components (Table, Button, Modal, etc.) instead of native HTML
- Tables use virtualization patterns with pagination
- Consistent `isIconOnly`, `variant="flat"`, `color="primary"` patterns
- Icons via `@iconify/react` with `lucide:` prefix

### State Management
- User data: `useUserStore()` (Zustand with persistence)
- Row counts: `useRowCountStore()` 
- Auth state: `useAuth()` hook handles JWT and permissions
- Form state: Local component state, not global store

## File Structure Conventions
- **Pages**: `app/` directory with layout.tsx files for nested layouts
- **Components**: Organized by feature (`projects/`, `auth/`, `users/`, etc.)
- **Types**: Separate files per domain (`types/projects.ts`, `types/user.ts`)
- **Hooks**: Feature-grouped in `hooks/[feature]/` directories
- **GraphQL**: Operations in `.graphql` files, not inline queries

## Development Workflow
1. **Dev server**: `bun run dev` (port 3003 with turbopack)
2. **GraphQL changes**: Update `.graphql` files → `bun run codegen`
3. **Auth testing**: Set `NEXT_PUBLIC_DISABLE_AUTH=true` to bypass auth
4. **Styling**: Use HeroUI theme system, dark/light mode via `next-themes`

## Error Handling Patterns
- GraphQL errors handled by Apollo client error policies
- Permission failures redirect to `/unauthorized`
- Missing auth redirects to `/login` via `Providers` component
- Form validation uses HeroUI form components with inline error states

## Key Files to Reference
- `components/projects/projects-table.tsx` - Complex data transformation example
- `components/primitives.ts` - UI styling utilities and status mappers
- `hooks/auth/useAuth.ts` - Permission checking patterns
- `app/providers.tsx` - Auth flow and provider setup
- `lib/apollo.tsx` - GraphQL client with auth headers
