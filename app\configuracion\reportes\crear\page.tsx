"use client";

import React, { useState, useEffect, Suspense } from "react";
import { Button } from "@heroui/react";
import { useRouter, useSearchParams } from "next/navigation";

import { title } from "@/components/primitives";
import ReportStepper from "@/components/report/create/report-stepper";
import SelectableFieldsTable from "@/components/template/create/selectable-fields-table";
import ReportFiltersStep from "@/components/report/create/report-filters-step";
import ReportSummary from "@/components/report/create/report-summary";
import ReportConfigStep from "@/components/report/create/report-config-step";
import { useReports } from "@/hooks/reports/useReports";
import { useReportDetail } from "@/hooks/reports/useReportDetail";

interface FieldTypeFilter {
  statuses: ("completed" | "not_completed" | "in_progress")[];
  includeObservations: boolean;
}

interface ReportConfig {
  name: string;
  name_en: string;
  description: string;
  description_en: string;
  documentName: string;
  documentName_en: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
  phases: boolean;
  subphases: boolean;
}

function ReportContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { createReport } = useReports();
  const { reportDetail, fetchReportDetail } = useReportDetail();

  const [currentStep, setCurrentStep] = useState(0);
  const [isConfigStepValid, setIsConfigStepValid] = useState(false);
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: searchParams.get("name") || "",
    name_en: searchParams.get("name_en") || "",
    description: searchParams.get("description") || "",
    description_en: searchParams.get("description_en") || "",
    documentName: searchParams.get("document_name") || "",
    documentName_en: searchParams.get("document_name_en") || "",
    useEnglishFields: false,
    includeObservations: true,
    phases: false,
    subphases: false,
  });
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [fieldFilters, setFieldFilters] = useState<
    Record<string, FieldTypeFilter>
  >({});
  const [hasAutofilledData, setHasAutofilledData] = useState(false);

  // Handle cloning from existing report
  useEffect(() => {
    const reportId = searchParams.get("id");

    if (reportId) {
      fetchReportDetail(reportId);
    }
  }, [searchParams]);

  // Populate data when cloning - only once on first load
  useEffect(() => {
    if (reportDetail && !hasAutofilledData) {
      // Only autofill if we haven't done it before and there are no search params for basic fields
      const hasNameParam = searchParams.get("name");
      const hasDescParam = searchParams.get("description");
      const hasDocParam = searchParams.get("document_name");

      setReportConfig({
        name: hasNameParam || `${reportDetail.name} (Copia)`,
        name_en: hasNameParam || `${reportDetail.name_en} (Copy)`,
        description: hasDescParam || reportDetail.description,
        description_en: hasDescParam || reportDetail.description_en,
        documentName: hasDocParam || `${reportDetail.document_name}_copy`,
        documentName_en: hasDocParam || `${reportDetail.document_name_en}_copy`,
        useEnglishFields: reportDetail.use_english || false,
        includeObservations: reportDetail.observations || true,
        phases: reportDetail.phases || false,
        subphases: reportDetail.subphases || false,
      });

      // Set selected fields
      if (reportDetail.fields) {
        setSelectedFields(reportDetail.fields.map((f) => f.id.toString()));
      }

      // Set field filters
      if (reportDetail.filters) {
        const filters: Record<string, FieldTypeFilter> = {};

        Object.entries(reportDetail.filters).forEach(
          ([fieldType, filterConfig]) => {
            if (filterConfig.statuses && filterConfig.statuses.length > 0) {
              filters[fieldType] = {
                statuses: filterConfig.statuses,
                includeObservations: filterConfig.includeObservations || false,
              };
            }
          },
        );
        setFieldFilters(filters);
      }

      // Mark that we've autofilled the data
      setHasAutofilledData(true);
    }
  }, [reportDetail, hasAutofilledData, searchParams]);

  const steps = [
    {
      key: 0,
      title: "Configuración",
      description: "Configura los datos básicos del reporte",
    },
    {
      key: 1,
      title: "Seleccionar campos",
      description: "Elige los campos para el reporte",
    },
    {
      key: 2,
      title: "Configurar filtros",
      description: "Define los filtros para cada campo",
    },
    {
      key: 3,
      title: "Confirmar creación",
      description: "Revisa y confirma el reporte",
    },
  ];

  const handleNext = () => {
    // Validate current step before proceeding
    if (currentStep === 0 && !isConfigStepValid) {
      return; // Don't proceed if config step is not valid
    }

    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (step: number) => {
    setCurrentStep(step);
  };

  const handleCreateReport = async () => {
    try {
      const result = await createReport({
        reportConfig,
        selectedFields: selectedFields,
        fieldFilters,
      });

      if (result.success) {
        router.push("/configuracion");
      } else {
        console.error("Failed to create report:", result.error);
        // TODO: Show error message to user
      }
    } catch (error) {
      console.error("Error creating report:", error);
      // TODO: Show error message to user
    }
  };

  const currentStepData = steps.find((step) => step.key === currentStep);
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === 3;
  const isNextDisabled = currentStep === 0 && !isConfigStepValid;

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <ReportConfigStep
            config={reportConfig}
            onConfigChange={setReportConfig}
            onValidationChange={setIsConfigStepValid}
          />
        );
      case 1:
        return (
          <SelectableFieldsTable
            selectedFields={selectedFields}
            onSelectionChange={setSelectedFields}
          />
        );
      case 2:
        return (
          <ReportFiltersStep
            fieldFilters={fieldFilters}
            selectedFields={selectedFields}
            onFiltersChange={setFieldFilters}
          />
        );
      case 3:
        return (
          <ReportSummary
            fieldFilters={fieldFilters}
            reportConfig={reportConfig}
            selectedFields={selectedFields}
            onCreateReport={handleCreateReport}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex justify-between items-center w-full pb-4">
        <div>
          <h2 className={title({ size: "sm" })}>
            Paso {currentStep + 1}: {currentStepData?.title}
          </h2>
          <p className="text-default-500 mt-1">
            {currentStepData?.description}
          </p>
        </div>
        <div className="flex gap-2">
          {!isFirstStep && (
            <Button variant="bordered" onPress={handlePrevious}>
              Anterior
            </Button>
          )}
          {!isLastStep && (
            <Button
              color="primary"
              isDisabled={isNextDisabled}
              onPress={handleNext}
            >
              Siguiente
            </Button>
          )}
        </div>
      </div>

      <ReportStepper
        currentStep={currentStep}
        steps={steps}
        onStepClick={handleStepClick}
      />

      {renderStepContent()}
    </>
  );
}

export default function CrearReportePage() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <Suspense fallback={<p>Cargando...</p>}>
        <ReportContent />
      </Suspense>
    </div>
  );
}
