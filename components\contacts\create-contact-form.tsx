"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Input,
  Select,
  SelectItem,
  Form,
  Autocomplete,
  AutocompleteItem,
} from "@heroui/react";
import { useQuery } from "@apollo/client";

import { GET_ALL_BASIC_PROJECTS_INFO } from "@/graphql/operations/projects";

interface CreateContactFormProps {
  onClose: () => void;
  onAddContact?: (contact: Omit<Contact, "id" | "created_at">) => void;
  projectId?: number | null;
}

interface Contact {
  id: number;
  name: string;
  email: string;
  type: string;
  position: string;
  project: number;
  created_at: string;
}

const contactTypes = [
  "Empresa",
  "Agregador",
  "Add-on",
  "OOS",
  "Impuestos",
  "SS",
  "GL File",
];

const CreateContactForm = ({
  onClose,
  onAddContact,
  projectId,
}: CreateContactFormProps) => {
  const { data: allProjects } = useQuery(GET_ALL_BASIC_PROJECTS_INFO);

  const [formData, setFormData] = useState({
    type: contactTypes[0],
    name: "",
    email: "",
    position: "",
    project: projectId || 0,
  });

  const [selectedProjectKey, setSelectedProjectKey] = useState<string | null>(
    projectId ? projectId.toString() : null,
  );
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = () => {
    if (onAddContact) {
      onAddContact({
        name: formData.name,
        email: formData.email,
        type: formData.type,
        position: formData.position,
        project: formData.project,
      });
    }
    onClose();
  };

  return (
    <>
      <ModalHeader>Crear Nuevo Contacto</ModalHeader>
      <Form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <ModalBody className="w-full">
          <div className="flex flex-col gap-4 w-full">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="project-select"
              >
                LID - Razón Social
              </label>
              {projectId ? (
                <Input
                  className="w-full"
                  id="project-select"
                  isDisabled={true}
                  name="project"
                  value={
                    allProjects?.allProjects?.find(
                      (p: any) => p.id === projectId,
                    )
                      ? `${allProjects.allProjects.find((p: any) => p.id === projectId)?.lid} - ${allProjects.allProjects.find((p: any) => p.id === projectId)?.companyName}`
                      : `Proyecto ID: ${projectId}`
                  }
                />
              ) : (
                <Autocomplete
                  className="w-full"
                  id="project-select"
                  placeholder="Selecciona un cliente"
                  selectedKey={selectedProjectKey}
                  variant="bordered"
                  onSelectionChange={(key) => {
                    const projectId = key ? Number(key) : 0;

                    setSelectedProjectKey(key ? String(key) : null);
                    setFormData((prev) => ({
                      ...prev,
                      project: projectId,
                    }));
                  }}
                >
                  {allProjects?.allProjects?.map((project: any) => (
                    <AutocompleteItem
                      key={project.id.toString()}
                      textValue={`${project.lid} - ${project.companyName}`}
                    >
                      <div className="text-default-700">
                        <div className="font-medium">
                          {project.lid} - {project.companyName}
                        </div>
                      </div>
                    </AutocompleteItem>
                  ))}
                </Autocomplete>
              )}
            </div>
            <div className="w-full">
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-type"
              >
                Tipo de contacto
              </label>
              <Select
                className="w-full"
                id="contact-type"
                name="type"
                placeholder="Selecciona un tipo"
                selectionMode="single"
                value={formData.type}
                onChange={handleChange}
              >
                {contactTypes.map((type) => (
                  <SelectItem key={type}>{type}</SelectItem>
                ))}
              </Select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-position"
              >
                Cargo
              </label>
              <Input
                className="w-full"
                id="contact-position"
                name="position"
                placeholder="Cargo o posición"
                value={formData.position}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-name"
              >
                Nombre
              </label>
              <Input
                className="w-full"
                id="contact-name"
                name="name"
                placeholder="Nombre completo"
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-email"
              >
                Correo electrónico
              </label>
              <Input
                className="w-full"
                id="contact-email"
                name="email"
                placeholder="<EMAIL>"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter className="justify-end w-full">
          <Button
            color="danger"
            type="button"
            variant="light"
            onPress={onClose}
          >
            Cancelar
          </Button>
          <Button color="primary" type="submit">
            Guardar
          </Button>
        </ModalFooter>
      </Form>
    </>
  );
};

export default CreateContactForm;
