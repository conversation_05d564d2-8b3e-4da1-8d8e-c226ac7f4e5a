"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  Input,
  Pa<PERSON>ation,
  Table,
  TableHeader,
  TableRow,
  TableCell,
  TableBody,
  TableColumn,
  Sort<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import React, { useState, useEffect } from "react";
import { useAsyncList } from "@react-stately/data";
import { Icon } from "@iconify/react";

import { FilterDropdown } from "../projects/projects-table/filter-dropdown";

import { RoleChangeModal } from "./role-change-modal";

import { User } from "@/hooks/users/useUsers";
import { Team } from "@/types/team";
import { Role } from "@/types/role";
import { useUsersList } from "@/hooks/users/useUsersList";
import { useRoles } from "@/hooks/users/useRoles";
import { useRowCountStore } from "@/store/use-row-count-store";

// Convert UserToTeam utility function to map User objects to Team objects
const mapUserToTeam = (user: User & { position?: string }): Team => {
  const defaultGroup =
    user.groups && user.groups.length > 0
      ? user.groups[0]
      : { name: "Sin rol" };

  return {
    id: user.id,
    nombre: user.name.split(" ")[0] || "",
    apellido: user.name.split(" ").slice(1).join(" ") || "",
    email: user.email,
    posicion: user.position === "True" ? "Sí" : "No",
    equipo: user.team_code || "N/A",
    rol: defaultGroup.name,
  };
};

interface UsersTableProps {
  canEditUsers?: boolean;
}

export default function UsersTable({ canEditUsers = false }: UsersTableProps) {
  const {
    users,
    loading: usersLoading,
    error: usersError,
    fetchUsers,
  } = useUsersList();
  const { updateUserRole } = useRoles();
  const { rowCount } = useRowCountStore();

  const [page, setPage] = useState(1);
  const rowsPerPage = rowCount || 10;
  const [searchTerm, setSearchTerm] = useState("");
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "nombre",
    direction: "ascending",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [currentData, setCurrentData] = useState<Team[]>([]);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );

  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<Team | null>(null);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);

  // Function to get unique values for filterDropdown
  const getUniqueValues = (column: keyof Team): string[] => {
    const uniqueValues = Array.from(
      new Set(
        currentData.map((item) => {
          const value = item[column];

          return typeof value === "string" ? value : String(value);
        }),
      ),
    );

    return uniqueValues;
  };

  // Function to handle filter changes
  const handleFilterChange = (column: string, values: string[]): void => {
    const newFilters = { ...activeFilters };

    if (values.length === 0) {
      delete newFilters[column];
    } else {
      newFilters[column] = values;
    }

    setActiveFilters(newFilters);

    // Apply filters to the data
    let filteredData = users?.map(mapUserToTeam) || [];

    // Apply search filter
    if (searchTerm) {
      filteredData = filteredData.filter(
        (item) =>
          item.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.apellido.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.posicion.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.equipo.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.rol.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.id.toString().includes(searchTerm),
      );
    }

    // Apply dropdown filters
    Object.entries(newFilters).forEach(([col, filterValues]) => {
      if (filterValues.length > 0) {
        filteredData = filteredData.filter((item) => {
          const itemValue = String(item[col as keyof Team]);

          return filterValues.includes(itemValue);
        });
      }
    });

    setCurrentData(filteredData);
    list.reload();
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    if (users && users.length > 0) {
      const mappedUsers = users.map(mapUserToTeam);

      setCurrentData(mappedUsers);
    }
  }, [users]);

  const list = useAsyncList<Team>({
    async load() {
      setIsLoading(usersLoading);

      return {
        items: currentData,
      };
    },
    async sort({ items, sortDescriptor }) {
      return {
        items: items.sort((a, b) => {
          const column = sortDescriptor.column as keyof Team;
          const direction = sortDescriptor.direction === "ascending" ? 1 : -1;

          if (typeof a[column] === "string") {
            return (
              direction *
              (a[column] as string).localeCompare(b[column] as string)
            );
          } else {
            return direction * ((a[column] as number) - (b[column] as number));
          }
        }),
      };
    },
  });

  useEffect(() => {
    if (sortDescriptor.column) {
      list.sort(sortDescriptor as any);
    }
  }, [sortDescriptor]);

  useEffect(() => {
    list.reload();
  }, [currentData]);

  useEffect(() => {
    setPage(1);
    list.reload();
  }, [searchTerm]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);

    if (value.trim()) {
      const filteredItems = currentData.filter(
        (item) =>
          item.nombre.toLowerCase().includes(value.toLowerCase()) ||
          item.apellido.toLowerCase().includes(value.toLowerCase()) ||
          item.email.toLowerCase().includes(value.toLowerCase()) ||
          item.posicion.toLowerCase().includes(value.toLowerCase()) ||
          item.equipo.toLowerCase().includes(value.toLowerCase()) ||
          item.rol.toLowerCase().includes(value.toLowerCase()) ||
          item.id.toString().includes(value),
      );

      setCurrentData(filteredItems);
      list.reload();
    } else {
      if (users && users.length > 0) {
        setCurrentData(users.map(mapUserToTeam));
      }
    }

    list.reload();
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setActiveFilters({});
    if (users && users.length > 0) {
      setCurrentData(users.map(mapUserToTeam));
    }
    list.reload();
  };

  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = list.items.slice(startIndex, endIndex);
  const pages = Math.ceil(list.items.length / rowsPerPage);

  const handleOpenRoleModal = (user: Team) => {
    setSelectedUser(user);
    setSelectedRole(user.rol);
    setIsRoleModalOpen(true);
  };

  const handleRoleUpdate = (updatedRole: Role) => {
    console.log("Updated role:", updatedRole);

    if (selectedUser) {
      const updatedData = currentData.map((user) => {
        if (user.id === selectedUser.id) {
          return {
            ...user,
            rol: updatedRole.name,
          };
        }

        return user;
      });

      updateUserRole(selectedUser.id, updatedRole.id)
        .then(() => {
          console.log("Role updated successfully");
        })
        .catch((error) => {
          console.error("Error updating role:", error);
        })
        .finally(() => {
          setIsLoading(false);
          list.reload();
          setIsRoleModalOpen(false);
          setCurrentData(updatedData);
        });
    }
  };

  return (
    <>
      <Card className="p-2 mb-4 w-full pt-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por nombre, apellido, email, posición, equipo o rol... (◕‿◕✿)"
              startContent={"🔍"}
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button
              color={
                searchTerm || Object.keys(activeFilters).length > 0
                  ? "primary"
                  : "default"
              }
              variant="flat"
              onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {(searchTerm || Object.keys(activeFilters).length > 0) &&
                `(${searchTerm ? 1 : 0 + Object.keys(activeFilters).length})`}
            </Button>
          </div>
        </div>
      </Card>

      <Table
        key={
          isRoleModalOpen
            ? "modal-open" + canEditUsers
            : "modal-closed" + canEditUsers
        }
        removeWrapper
        aria-label="Tabla del equipo"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              isCompact
              showControls
              showShadow
              color="primary"
              page={page}
              total={pages}
              onChange={(page) => setPage(page)}
            />
          </div>
        }
        sortDescriptor={sortDescriptor as any}
        onSortChange={setSortDescriptor}
      >
        <TableHeader>
          <TableColumn key="nombre">
            <FilterDropdown
              activeFilters={activeFilters}
              column="nombre"
              items={getUniqueValues("nombre")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="NOMBRE"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="apellido">
            <FilterDropdown
              activeFilters={activeFilters}
              column="apellido"
              items={getUniqueValues("apellido")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="APELLIDO"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="email">
            <FilterDropdown
              activeFilters={activeFilters}
              column="email"
              items={getUniqueValues("email")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="EMAIL"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="equipo">
            <FilterDropdown
              activeFilters={activeFilters}
              column="equipo"
              items={getUniqueValues("equipo")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="EQUIPO"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="posicion">
            <FilterDropdown
              activeFilters={activeFilters}
              column="posicion"
              items={getUniqueValues("posicion")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="LÍDER"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="rol">
            <FilterDropdown
              activeFilters={activeFilters}
              column="rol"
              items={getUniqueValues("rol")}
              sortConfig={{
                column: sortDescriptor.column as string,
                direction:
                  sortDescriptor.direction === "ascending" ? "asc" : "desc",
              }}
              title="ROL"
              onFilter={handleFilterChange}
              onSort={(column, direction) =>
                setSortDescriptor({
                  column,
                  direction: direction === "asc" ? "ascending" : "descending",
                })
              }
            />
          </TableColumn>
          <TableColumn key="actions">ACCIONES</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent={usersError ? "Error loading users" : "No users found"}
          isLoading={usersLoading}
          items={paginatedItems}
          loadingContent={<Spinner label="Cargando usuarios..." />}
        >
          {(item) => (
            <TableRow key={item.id}>
              <TableCell>{item.nombre}</TableCell>
              <TableCell>{item.apellido}</TableCell>
              <TableCell>{item.email}</TableCell>
              <TableCell>{item.equipo}</TableCell>
              <TableCell>{item.posicion}</TableCell>
              <TableCell>{item.rol}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    color="primary"
                    isDisabled={!canEditUsers}
                    size="sm"
                    startContent={
                      <Icon className="text-lg" icon="lucide:edit" />
                    }
                    variant="flat"
                    onPress={() => handleOpenRoleModal(item)}
                  />
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <RoleChangeModal
        isOpen={isRoleModalOpen}
        mode="edit"
        role={selectedRole}
        onClose={() => setIsRoleModalOpen(false)}
        onConfirm={handleRoleUpdate}
      />
    </>
  );
}
