"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Input, Button, Card, Checkbox, Spacer } from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAuth } from "@/hooks/auth/useAuth";

export default function LoginPage() {
  const { login, user } = useAuth();
  const router = useRouter();

  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [rememberMe, setRememberMe] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isVisible, setIsVisible] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push("/");
    }
  }, [user, router]);

  const toggleVisibility = () => setIsVisible(!isVisible);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage(null); // Clear any previous error messages

    try {
      const res = await login(email, password);

      console.log("Login successful");
      // Redirect to the dashboard or home page after successful login
      if (res) router.push("/");
    } catch (error: any) {
      console.error("Login failed:", error);
      setErrorMessage(
        error.message ||
          "Error al iniciar sesión. Por favor, verifica tus credenciales.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    router.push("/login/reiniciar");
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center">
      <Card className="w-full max-w-md p-6 space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">Bienvenido a RMO</h1>
          <p className="text-default-500">
            Por favor, ingresa tus datos para iniciar sesión
          </p>
        </div>

        {errorMessage && (
          <div
            className="bg-danger-50 border border-danger-200 text-danger-700 px-4 py-3 rounded relative"
            role="alert"
          >
            <span className="block sm:inline">{errorMessage}</span>
          </div>
        )}

        <form className="space-y-4" onSubmit={handleLogin}>
          <Input
            isRequired
            label="Correo electrónico"
            placeholder="Ingresa tu correo electrónico"
            startContent={
              <Icon
                className="text-default-400"
                icon="lucide:mail"
                width={20}
              />
            }
            type="text"
            value={email}
            onValueChange={setEmail}
          />

          <Input
            isRequired
            endContent={
              <button type="button" onClick={toggleVisibility}>
                <Icon
                  className="text-default-400"
                  icon={isVisible ? "lucide:eye" : "lucide:eye-off"}
                  width={20}
                />
              </button>
            }
            label="Contraseña"
            placeholder="Ingresa tu contraseña"
            startContent={
              <Icon
                className="text-default-400"
                icon="lucide:lock"
                width={20}
              />
            }
            type={isVisible ? "text" : "password"}
            value={password}
            onValueChange={setPassword}
          />

          <Spacer y={2} />

          <Button
            fullWidth
            color="primary"
            isLoading={isLoading}
            size="lg"
            type="submit"
          >
            Iniciar sesión
          </Button>
        </form>
      </Card>
    </div>
  );
}
