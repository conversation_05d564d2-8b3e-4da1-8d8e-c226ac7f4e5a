"use client";

import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";

export default function UnauthorizedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const code = searchParams?.get("code") || "403";

  const messages = {
    "401": "No has iniciado sesión o tu sesión ha expirado.",
    "403": "No tienes permisos para acceder a este recurso.",
    "404": "El recurso solicitado no existe.",
  };

  const message =
    messages[code as keyof typeof messages] ||
    "No tienes acceso a este recurso.";

  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center px-4">
      <Icon
        className="text-8xl text-warning mb-6"
        icon={code === "403" ? "lucide:lock" : "lucide:alert-triangle"}
      />

      <h1 className="text-4xl font-bold mb-4">Acceso Denegado</h1>

      <p className="text-xl mb-8 text-default-500 max-w-md">{message}</p>

      <div className="flex flex-wrap gap-4 justify-center">
        <Button
          color="primary"
          size="lg"
          startContent={<Icon icon="lucide:home" />}
          onPress={() => router.push("/")}
        >
          Volver al inicio
        </Button>

        {code === "401" && (
          <Button
            size="lg"
            startContent={<Icon icon="lucide:log-in" />}
            variant="bordered"
            onPress={() => router.push("/login")}
          >
            Iniciar sesión
          </Button>
        )}
      </div>
    </div>
  );
}
