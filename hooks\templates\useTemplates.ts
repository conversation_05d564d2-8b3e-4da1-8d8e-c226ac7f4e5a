"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface ApiTemplate {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

export interface CreateTemplateData {
  templateData: { name: string; description: string };
  selectedFields: number[];
  selectedRules: number[];
  selectedNotifications: number[];
}

export interface UpdateTemplateData {
  selectedRules: number[];
  selectedNotifications: number[];
}

export function useTemplates() {
  const [templates, setTemplates] = useState<ApiTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_TEMPLATES);

      if (response.status === 200) {
        setTemplates(response.data);
      } else {
        setError("Failed to fetch templates");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch templates");
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_ACTIVE_TEMPLATES);

      if (response.status === 200) {
        setTemplates(response.data);
      } else {
        setError("Failed to fetch templates");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch templates");
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (
    templateData: CreateTemplateData,
  ): Promise<ApiTemplate | null> => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.post(
        API_ROUTES.CREATE_TEMPLATE,
        templateData,
      );

      if (response.status === 201) {
        // Update templates state with the new template
        // setTemplates((prevTemplates) => [...prevTemplates, response.data]);
        return response.data;
      } else {
        setError("Failed to create template");

        return null;
      }
    } catch (err: any) {
      setError(err.message || "Failed to create template");

      return null;
    } finally {
      setLoading(false);
    }
  };

  const disableTemplate = async (templateId: number) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.DISABLE_TEMPLATE.replace(
        "{id}",
        templateId.toString(),
      );
      const response = await axiosInstance.post(url);

      if (response.status === 200) {
        // Refresh the templates list
        await fetchTemplates();

        return { success: true, data: response.data };
      } else {
        setError("Failed to disable template");

        return { success: false, error: "Failed to disable template" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to disable template";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateTemplate = async (
    templateId: number,
    updateData: UpdateTemplateData,
  ): Promise<{ success: boolean; data?: any; error?: string }> => {
    try {
      setLoading(true);
      setError(null);
      const url = API_ROUTES.UPDATE_TEMPLATE.replace(
        "{id}",
        templateId.toString(),
      );
      const response = await axiosInstance.post(url, {
        rules: updateData.selectedRules,
        notifications: updateData.selectedNotifications,
      });

      if (response.status === 200) {
        // Refresh the templates list to get updated data
        await fetchTemplates();

        return { success: true, data: response.data };
      } else {
        setError("Failed to update template");

        return { success: false, error: "Failed to update template" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update template";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    templates,
    loading,
    error,
    fetchTemplates,
    fetchActiveTemplates,
    createTemplate,
    disableTemplate,
    updateTemplate,
  };
}
