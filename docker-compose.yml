services:
  next-app:
    container_name: rmo-front
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_GRAPHQL_URI: ${NEXT_PUBLIC_GRAPHQL_URI}
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
        ENVIRONMENT: ${ENVIRONMENT}
    restart: always
    environment:
      - NEXT_PUBLIC_GRAPHQL_URI=${NEXT_PUBLIC_GRAPHQL_URI}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - ENVIRONMENT=Local Production
    env_file:
      - .env
    ports:
      - 3000:3000