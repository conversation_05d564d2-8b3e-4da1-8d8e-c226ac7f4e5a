"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

interface EmailTopic {
  id: number;
  name: string;
}

export function useEmailTopics() {
  const [topics, setTopics] = useState<EmailTopic[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEmailTopics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.EMAIL_TOPICS);

      if (response.status === 200) {
        setTopics(response.data);
      } else {
        setError("Failed to fetch email topics");
      }

      return response.data;
    } catch (err: any) {
      setError(err.message || "Failed to fetch email topics");
    } finally {
      setLoading(false);
    }

    return [];
  };

  return {
    topics,
    loading,
    error,
    fetchEmailTopics,
  };
}
