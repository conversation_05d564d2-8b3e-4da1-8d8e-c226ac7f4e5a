export interface Field {
  id: number;
  name: string;
  description: string;
  field_type:  "INFORMATIVE" | "SELECTION" | "TASK" | "DOCUMENT" | "TASK_WITH_SUBTASKS";
  phase:
    | "Start"
    | "Collection"
    | "Migration"
    | "Go Live"
    | "Test"
    | "Incubadora";
  subphase: string;
  hito: string;
  weight: number;

  value?: string;
}

export interface FieldProps {
  id: number;
  title: string;
  description: string;
  type: "INFORMATIVE" | "SELECTION" | "TASK" | "DOCUMENT" | "TASK_WITH_SUBTASKS";
  observation: string;
  milestone: boolean;
  options?: { key: string; label: string, countAsCompleted: boolean}[];
  edit?: boolean;
  subtasks?: {
    id: number;
    title: string;
    description: string;
    observation: string;
    value: string;
  }[];
  is_active?: boolean;
  value?: string;
}
