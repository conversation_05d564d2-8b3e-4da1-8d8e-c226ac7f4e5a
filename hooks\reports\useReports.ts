"use client";

import { useState } from "react";

import { ApiReport, CreateReportData, UpdateReportData } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReports() {
  const [reports, setReports] = useState<ApiReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getActiveReports();

      setReports(data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch reports");
    } finally {
      setLoading(false);
    }
  };

  const createReport = async (
    reportData: CreateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const newReport = await ReportsService.createReport(reportData);

      setReports((prevReports) => [...prevReports, newReport]);

      return { success: true, data: newReport };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to create report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateReport = async (
    id: number,
    updateData: UpdateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const updatedReport = await ReportsService.updateReport(id, updateData);

      setReports((prevReports) =>
        prevReports.map((report) =>
          report.id === id ? updatedReport : report,
        ),
      );

      return { success: true, data: updatedReport };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const deleteReport = async (
    id: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      await ReportsService.deleteReport(id);
      setReports((prevReports) =>
        prevReports.filter((report) => report.id !== id),
      );

      return { success: true };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to delete report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const toggleReportStatus = async (
    id: number,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const updatedReport = await ReportsService.toggleReportStatus(id);

      setReports((prevReports) =>
        prevReports.map((report) =>
          report.id === id ? updatedReport : report,
        ),
      );

      return { success: true, data: updatedReport };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to toggle report status";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const fetchAllReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getAllReports();

      setReports(data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch all reports");
    } finally {
      setLoading(false);
    }
  };

  const fetchSingleReport = async (
    id: number,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getReport(id);

      return { success: true, data };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to fetch report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    reports,
    loading,
    error,
    fetchReports,
    fetchAllReports,
    fetchSingleReport,
    createReport,
    updateReport,
    deleteReport,
    toggleReportStatus,
  };
}
