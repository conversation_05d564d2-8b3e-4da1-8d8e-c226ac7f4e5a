"use client";

import { useState } from "react";

import { ApiReport } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReportDetail() {
  const [reportDetail, setReportDetail] = useState<ApiReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReportDetail = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getReport(parseInt(id));

      setReportDetail(data);

      return data;
    } catch (err: any) {
      console.warn("Failed to fetch report, using fallback data", err);
      setReportDetail(null);

      return null;
    } finally {
      setLoading(false);
    }
  };

  const clearReportDetail = () => {
    setReportDetail(null);
    setError(null);
  };

  return {
    reportDetail,
    loading,
    error,
    fetchReportDetail,
    clearReportDetail,
  };
}
