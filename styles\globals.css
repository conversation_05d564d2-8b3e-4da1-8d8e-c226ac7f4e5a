@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar for dropdown menus */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Default custom scrollbar for the entire website */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* Default scrollbar for Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Montserrat Regular */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Montserrat Thin */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Thin Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

/* Montserrat ExtraLight */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

/* Montserrat ExtraLight Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-ExtraLightItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

/* Montserrat Light */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Light Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

/* Montserrat Medium */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Medium Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

/* Montserrat SemiBold */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Montserrat SemiBold Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

/* Montserrat Bold */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Bold Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* Montserrat ExtraBold */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Montserrat ExtraBold Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}

/* Montserrat Black */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Montserrat Black Italic */
@font-face {
  font-family: 'Montserrat';
  src: url('/fonts/Montserrat/Montserrat-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* Task highlight animation */
@keyframes taskHighlight {
  0% {
    border-color: transparent;
    box-shadow: 0 0 0 rgba(59, 130, 246, 0);
    transform: scale(1);
  }
  50% {
    border-color: #3b82f6;
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
    transform: scale(1.02);
  }
  100% {
    border-color: #3b82f6;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    transform: scale(1.02);
  }
}

.task-highlight {
  border: 3px solid #3b82f6 !important;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3) !important;
  transform: scale(1.02) !important;
  transition: all 0.3s ease !important;
  animation: taskHighlight 0.6s ease-in-out !important;
}

.task-highlight-fade-out {
  border: 3px solid transparent !important;
  box-shadow: 0 0 0 rgba(59, 130, 246, 0) !important;
  transform: scale(1) !important;
  transition: all 0.5s ease !important;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.rainbow-text {
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #8b00ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: rainbow 6s ease infinite;
  background-size: 400% 400%;
}

@keyframes rainbow {
  0% { background-position: 0% 50% }
  50% { background-position: 100% 50% }
  100% { background-position: 0% 50% }
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}