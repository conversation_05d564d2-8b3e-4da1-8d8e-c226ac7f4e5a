import type { Role } from "@/types/role";

import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Select,
  SelectItem,
} from "@nextui-org/react";

import { useRoles } from "@/hooks/users/useRoles";

interface RoleChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: string | null;
  onConfirm: (role: Role) => void;
  mode: "edit" | "delete";
}

export function RoleChangeModal({
  isOpen,
  onClose,
  role,
  onConfirm,
  mode,
}: RoleChangeModalProps) {
  const { roles, loading, fetchRoles } = useRoles();
  const [selectedRole, setSelectedRole] = React.useState<Role | null>(null);

  useEffect(() => {
    fetchRoles();
  }, []);

  useEffect(() => {
    if (role && roles.length > 0) {
      const foundRole = roles.find((r) => r.name === role);

      if (foundRole) {
        setSelectedRole(foundRole);
      }
    }
  }, [role, roles]);

  const handleConfirm = () => {
    if (mode === "delete" && selectedRole) {
      onConfirm(selectedRole);
    } else if (mode === "edit" && selectedRole) {
      onConfirm(selectedRole);
    }
    onClose();
  };

  return (
    <Modal isDismissable={false} isOpen={isOpen} onClose={onClose}>
      <ModalContent>
        <ModalHeader>
          {mode === "edit" ? "Editar rol" : "Eliminar rol"}
        </ModalHeader>
        <ModalBody>
          {mode === "edit" ? (
            <div className="flex flex-col gap-4">
              <Select
                isDisabled={loading || roles.length === 0}
                isLoading={loading}
                label="Seleccionar rol"
                placeholder={
                  loading ? "Cargando roles..." : "Selecciona un rol"
                }
                selectedKeys={selectedRole ? [selectedRole.id.toString()] : []}
                onChange={(e) => {
                  const roleId = parseInt(e.target.value);
                  const foundRole = roles.find((r) => r.id === roleId);

                  if (foundRole) {
                    setSelectedRole(foundRole);
                  }
                }}
              >
                {roles.map((roleItem) => (
                  <SelectItem key={roleItem.id} value={roleItem.id}>
                    {roleItem.name}
                  </SelectItem>
                ))}
              </Select>
            </div>
          ) : (
            <p>
              ¿Estás seguro que quieres eliminar el rol &quot;
              {selectedRole?.name}&quot;?
            </p>
          )}
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={onClose}>
            Cancelar
          </Button>
          <Button
            color={mode === "delete" ? "danger" : "primary"}
            onPress={handleConfirm}
          >
            {mode === "delete" ? "Eliminar" : "Guardar"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
