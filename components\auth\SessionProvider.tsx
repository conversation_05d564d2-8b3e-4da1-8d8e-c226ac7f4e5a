"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { useAuth } from "@/hooks/auth/useAuth";

interface SessionContextType {
  isAuthenticated: boolean;
  user: any;
  login: (email: string, password: string) => Promise<any>;
  logout: () => void;
  loading: boolean;
}

const SessionContext = createContext<SessionContextType>({
  isAuthenticated: false,
  user: null,
  login: async () => {},
  logout: () => {},
  loading: true,
});

export const useSession = () => useContext(SessionContext);

export default function SessionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, login, logout, loading, isAuthenticated } = useAuth();
  const [authState, setAuthState] = useState<boolean>(false);

  useEffect(() => {
    const checkAuth = async () => {
      const result = await isAuthenticated();

      setAuthState(result);
    };

    checkAuth();
  }, [isAuthenticated]);

  return (
    <SessionContext.Provider
      value={{
        isAuthenticated: authState,
        user,
        login,
        logout,
        loading,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}
