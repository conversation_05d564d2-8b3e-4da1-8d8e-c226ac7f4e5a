{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack --port 3003", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"@apollo/client": "^3.13.8", "@bitnoi.se/react-scheduler": "^0.3.1", "@heroui/accordion": "^2.2.13", "@heroui/alert": "^2.2.16", "@heroui/autocomplete": "^2.3.17", "@heroui/avatar": "^2.2.12", "@heroui/badge": "^2.2.10", "@heroui/breadcrumbs": "^2.2.12", "@heroui/button": "2.2.16", "@heroui/calendar": "^2.2.16", "@heroui/card": "^2.2.15", "@heroui/checkbox": "^2.3.15", "@heroui/chip": "^2.2.12", "@heroui/code": "2.2.12", "@heroui/date-input": "^2.3.15", "@heroui/date-picker": "^2.3.16", "@heroui/divider": "^2.2.11", "@heroui/drawer": "^2.2.13", "@heroui/dropdown": "^2.3.16", "@heroui/form": "^2.1.15", "@heroui/image": "^2.2.10", "@heroui/input": "2.4.16", "@heroui/input-otp": "^2.1.15", "@heroui/kbd": "2.2.12", "@heroui/link": "2.2.13", "@heroui/listbox": "2.3.15", "@heroui/menu": "^2.2.15", "@heroui/modal": "^2.2.13", "@heroui/navbar": "2.2.14", "@heroui/number-input": "^2.0.6", "@heroui/pagination": "^2.2.14", "@heroui/popover": "^2.3.16", "@heroui/progress": "^2.2.12", "@heroui/radio": "^2.3.15", "@heroui/react": "^2.7.5", "@heroui/ripple": "^2.2.12", "@heroui/scroll-shadow": "^2.3.10", "@heroui/select": "^2.4.16", "@heroui/skeleton": "^2.2.10", "@heroui/slider": "^2.4.13", "@heroui/snippet": "2.2.17", "@heroui/spacer": "^2.2.12", "@heroui/spinner": "^2.2.13", "@heroui/switch": "2.2.14", "@heroui/system": "2.4.12", "@heroui/table": "^2.2.15", "@heroui/tabs": "^2.2.13", "@heroui/theme": "2.4.12", "@heroui/toast": "^2.0.6", "@heroui/tooltip": "^2.2.13", "@heroui/user": "^2.2.12", "@iconify/react": "^5.2.1", "@nextui-org/react": "^2.6.11", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.20", "@react-stately/data": "^3.12.2", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.175.0", "a": "^3.0.1", "axios": "^1.9.0", "clsx": "2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.2", "graph": "^0.2.0", "graphql": "^16.11.0", "install": "^0.13.0", "intl-messageformat": "^10.5.0", "maath": "^0.10.8", "next": "15.0.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "18.3.1", "react-dom": "18.3.1", "react-icons": "^5.5.0", "three": "^0.175.0", "zustand": "^5.0.3"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/bun": "latest", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}