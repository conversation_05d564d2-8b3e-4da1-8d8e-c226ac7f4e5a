"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface Team {
  id: number;
  name: string;
  code: string;
}

export function useTeams() {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_TEAMS);

      if (response.status === 200) {
        setTeams(response.data);
      } else {
        setError("Failed to fetch teams");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch teams");
    } finally {
      setLoading(false);
    }
  };

  return {
    teams,
    loading,
    error,
    fetchTeams,
  };
}
